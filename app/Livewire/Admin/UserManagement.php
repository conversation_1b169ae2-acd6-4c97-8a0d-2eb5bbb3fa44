<?php

namespace App\Livewire\Admin;

use App\Models\Branch;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Validate;

class UserManagement extends Component
{
    use WithPagination;

    // Search and filters
    public $search = '';
    public $roleFilter = '';
    public $branchFilter = '';
    public $statusFilter = '';

    // Modal states
    public $showModal = false;
    public $editMode = false;
    public $selectedUser = null;

    // Form fields
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|email|unique:users,email')]
    public $email = '';

    #[Validate('required|string|min:8')]
    public $password = '';

    #[Validate('required|in:admin,manager,field_officer,member')]
    public $role = '';

    #[Validate('nullable|exists:branches,id')]
    public $branch_id = '';

    #[Validate('boolean')]
    public $is_active = true;

    // Bulk actions
    public $selectedUsers = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'roleFilter' => ['except' => ''],
        'branchFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedRoleFilter()
    {
        $this->resetPage();
    }

    public function updatedBranchFilter()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedUsers = $this->getUsers()->pluck('id')->toArray();
        } else {
            $this->selectedUsers = [];
        }
    }

    public function getUsers()
    {
        return User::query()
            ->with(['branch'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->roleFilter, function ($query) {
                $query->where('role', $this->roleFilter);
            })
            ->when($this->branchFilter, function ($query) {
                $query->where('branch_id', $this->branchFilter);
            })
            ->when($this->statusFilter !== '', function ($query) {
                $query->where('is_active', $this->statusFilter);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);
    }

    public function openModal()
    {
        $this->resetForm();
        $this->editMode = false;
        $this->showModal = true;
    }

    public function editUser($userId)
    {
        $user = User::findOrFail($userId);
        $this->selectedUser = $user;
        $this->editMode = true;
        
        $this->name = $user->name;
        $this->email = $user->email;
        $this->role = $user->role;
        $this->branch_id = $user->branch_id;
        $this->is_active = $user->is_active;
        $this->password = ''; // Don't populate password
        
        $this->showModal = true;
    }

    public function saveUser()
    {
        $this->validate();

        if ($this->editMode) {
            $this->updateUser();
        } else {
            $this->createUser();
        }

        $this->closeModal();
        session()->flash('message', $this->editMode ? 'User updated successfully!' : 'User created successfully!');
    }

    private function createUser()
    {
        User::create([
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
            'role' => $this->role,
            'branch_id' => $this->branch_id ?: null,
            'is_active' => $this->is_active,
        ]);
    }

    private function updateUser()
    {
        $data = [
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
            'branch_id' => $this->branch_id ?: null,
            'is_active' => $this->is_active,
        ];

        if (!empty($this->password)) {
            $data['password'] = Hash::make($this->password);
        }

        $this->selectedUser->update($data);
    }

    public function deleteUser($userId)
    {
        $user = User::findOrFail($userId);
        
        if ($user->id === auth()->id()) {
            session()->flash('error', 'You cannot delete your own account!');
            return;
        }

        $user->delete();
        session()->flash('message', 'User deleted successfully!');
    }

    public function toggleUserStatus($userId)
    {
        $user = User::findOrFail($userId);
        
        if ($user->id === auth()->id()) {
            session()->flash('error', 'You cannot deactivate your own account!');
            return;
        }

        $user->update(['is_active' => !$user->is_active]);
        session()->flash('message', 'User status updated successfully!');
    }

    public function bulkActivate()
    {
        User::whereIn('id', $this->selectedUsers)
            ->where('id', '!=', auth()->id())
            ->update(['is_active' => true]);
        
        $this->selectedUsers = [];
        $this->selectAll = false;
        session()->flash('message', 'Selected users activated successfully!');
    }

    public function bulkDeactivate()
    {
        User::whereIn('id', $this->selectedUsers)
            ->where('id', '!=', auth()->id())
            ->update(['is_active' => false]);
        
        $this->selectedUsers = [];
        $this->selectAll = false;
        session()->flash('message', 'Selected users deactivated successfully!');
    }

    public function bulkDelete()
    {
        User::whereIn('id', $this->selectedUsers)
            ->where('id', '!=', auth()->id())
            ->delete();
        
        $this->selectedUsers = [];
        $this->selectAll = false;
        session()->flash('message', 'Selected users deleted successfully!');
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->name = '';
        $this->email = '';
        $this->password = '';
        $this->role = '';
        $this->branch_id = '';
        $this->is_active = true;
        $this->selectedUser = null;
        $this->resetValidation();
    }

    public function render()
    {
        return view('livewire.admin.user-management', [
            'users' => $this->getUsers(),
            'branches' => Branch::all(),
            'roles' => [
                'admin' => 'Administrator',
                'manager' => 'Branch Manager',
                'field_officer' => 'Field Officer',
                'member' => 'Member',
            ],
        ]);
    }
}
