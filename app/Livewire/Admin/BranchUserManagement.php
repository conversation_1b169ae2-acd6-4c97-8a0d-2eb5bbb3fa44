<?php

namespace App\Livewire\Admin;

use App\Models\Branch;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class BranchUserManagement extends Component
{
    use WithPagination;

    public $selectedBranch = null;
    public $search = '';
    public $roleFilter = '';

    // Assignment modal
    public $showAssignModal = false;
    public $selectedUser = null;
    public $assignToBranch = '';

    // Manager assignment
    public $showManagerModal = false;
    public $selectedBranchForManager = null;
    public $selectedManager = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'roleFilter' => ['except' => ''],
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedRoleFilter()
    {
        $this->resetPage();
    }

    public function updatedSelectedBranch()
    {
        $this->resetPage();
    }

    public function getUsers()
    {
        return User::query()
            ->with(['branch'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->roleFilter, function ($query) {
                $query->where('role', $this->roleFilter);
            })
            ->when($this->selectedBranch, function ($query) {
                if ($this->selectedBranch === 'unassigned') {
                    $query->whereNull('branch_id');
                } else {
                    $query->where('branch_id', $this->selectedBranch);
                }
            })
            ->where('is_active', true)
            ->orderBy('name')
            ->paginate(10);
    }

    public function openAssignModal($userId)
    {
        $this->selectedUser = User::findOrFail($userId);
        $this->assignToBranch = $this->selectedUser->branch_id ?? '';
        $this->showAssignModal = true;
    }

    public function assignUserToBranch()
    {
        $this->validate([
            'assignToBranch' => 'nullable|exists:branches,id',
        ]);

        $this->selectedUser->update([
            'branch_id' => $this->assignToBranch ?: null,
        ]);

        $this->closeAssignModal();
        session()->flash('message', 'User branch assignment updated successfully!');
    }

    public function removeUserFromBranch($userId)
    {
        $user = User::findOrFail($userId);
        
        // Check if user is a branch manager
        $managedBranches = Branch::where('manager_id', $userId)->count();
        if ($managedBranches > 0) {
            session()->flash('error', 'Cannot remove user from branch. User is managing one or more branches.');
            return;
        }

        $user->update(['branch_id' => null]);
        session()->flash('message', 'User removed from branch successfully!');
    }

    public function openManagerModal($branchId)
    {
        $this->selectedBranchForManager = Branch::findOrFail($branchId);
        $this->selectedManager = $this->selectedBranchForManager->manager_id ?? '';
        $this->showManagerModal = true;
    }

    public function assignBranchManager()
    {
        $this->validate([
            'selectedManager' => 'nullable|exists:users,id',
        ]);

        // If assigning a new manager, ensure they have manager role and are in the branch
        if ($this->selectedManager) {
            $manager = User::findOrFail($this->selectedManager);
            
            if ($manager->role !== 'manager') {
                session()->flash('error', 'Selected user must have manager role.');
                return;
            }

            // Assign manager to branch if not already assigned
            if ($manager->branch_id !== $this->selectedBranchForManager->id) {
                $manager->update(['branch_id' => $this->selectedBranchForManager->id]);
            }
        }

        $this->selectedBranchForManager->update([
            'manager_id' => $this->selectedManager ?: null,
        ]);

        $this->closeManagerModal();
        session()->flash('message', 'Branch manager assigned successfully!');
    }

    public function closeAssignModal()
    {
        $this->showAssignModal = false;
        $this->selectedUser = null;
        $this->assignToBranch = '';
    }

    public function closeManagerModal()
    {
        $this->showManagerModal = false;
        $this->selectedBranchForManager = null;
        $this->selectedManager = '';
    }

    public function render()
    {
        return view('livewire.admin.branch-user-management', [
            'users' => $this->getUsers(),
            'branches' => Branch::with(['manager'])->get(),
            'availableManagers' => User::where('role', 'manager')
                ->where('is_active', true)
                ->orderBy('name')
                ->get(),
            'roles' => [
                'admin' => 'Administrator',
                'manager' => 'Branch Manager',
                'field_officer' => 'Field Officer',
                'member' => 'Member',
            ],
        ]);
    }
}
