<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Installment;
use App\Models\BranchTransaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class AdminDashboard extends Component
{
    public $selectedPeriod = '30'; // days
    public $selectedBranch = '';
    
    // KPI Data
    public $systemOverview = [];
    public $financialSummary = [];
    public $branchPerformance = [];
    public $userActivity = [];
    public $criticalAlerts = [];
    public $growthMetrics = [];
    public $installmentTrends = [];
    public $defaultRates = [];

    public function mount()
    {
        $this->loadSystemOverview();
        $this->loadFinancialSummary();
        $this->loadBranchPerformance();
        $this->loadUserActivity();
        $this->loadCriticalAlerts();
        $this->loadGrowthMetrics();
        $this->loadInstallmentTrends();
        $this->loadDefaultRates();
    }

    public function updatedSelectedPeriod()
    {
        $this->loadFinancialSummary();
        $this->loadBranchPerformance();
        $this->loadGrowthMetrics();
        $this->loadInstallmentTrends();
        $this->loadDefaultRates();
    }

    public function updatedSelectedBranch()
    {
        $this->loadBranchPerformance();
    }

    public function loadSystemOverview()
    {
        $this->systemOverview = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'total_branches' => Branch::count(),
            'total_members' => Member::where('is_active', true)->count(),
            'active_loans' => Loan::whereHas('loanApplication', function($q) {
                $q->where('status', 'approved');
            })->count(),
            'pending_applications' => LoanApplication::where('status', 'pending')->count(),
            'total_loan_amount' => Loan::whereHas('loanApplication', function($q) {
                $q->where('status', 'approved');
            })->sum('loan_amount'),
            'total_collections_today' => Installment::whereDate('collection_date', today())
                ->where('status', 'paid')
                ->sum('installment_amount'),
        ];
    }

    public function loadFinancialSummary()
    {
        $startDate = Carbon::now()->subDays($this->selectedPeriod);
        
        $this->financialSummary = [
            'total_disbursed' => Loan::whereHas('loanApplication', function($q) {
                $q->where('status', 'approved');
            })->where('loan_date', '>=', $startDate)->sum('loan_amount'),
            
            'total_collected' => Installment::where('status', 'paid')
                ->where('collection_date', '>=', $startDate)
                ->sum('installment_amount'),
                
            'outstanding_amount' => $this->calculateOutstandingAmount(),
            
            'collection_efficiency' => $this->calculateCollectionEfficiency($startDate),
            
            'default_rate' => $this->calculateDefaultRate(),
            
            'profit_margin' => $this->calculateProfitMargin($startDate),
        ];
    }

    public function loadBranchPerformance()
    {
        $query = Branch::with(['members', 'users'])
            ->withCount(['members as active_members_count' => function($q) {
                $q->where('is_active', true);
            }]);

        if ($this->selectedBranch) {
            $query->where('id', $this->selectedBranch);
        }

        $branches = $query->get();
        
        $this->branchPerformance = $branches->map(function($branch) {
            $startDate = Carbon::now()->subDays($this->selectedPeriod);
            
            $totalLoans = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->where('loan_date', '>=', $startDate)->sum('loan_amount');
            
            $totalCollections = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->where('collection_date', '>=', $startDate)
              ->where('status', 'paid')
              ->sum('installment_amount');
            
            $overdueAmount = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->where('status', 'overdue')->sum('installment_amount');
            
            return [
                'id' => $branch->id,
                'name' => $branch->name,
                'manager' => $branch->manager->name ?? 'Not Assigned',
                'active_members' => $branch->active_members_count,
                'field_officers' => $branch->users()->where('role', 'field_officer')->count(),
                'total_loans' => $totalLoans,
                'total_collections' => $totalCollections,
                'overdue_amount' => $overdueAmount,
                'collection_rate' => $totalLoans > 0 ? round(($totalCollections / $totalLoans) * 100, 2) : 0,
            ];
        })->toArray();
    }

    public function loadUserActivity()
    {
        $this->userActivity = [
            'recent_logins' => User::whereNotNull('email_verified_at')
                ->where('updated_at', '>=', Carbon::now()->subDays(7))
                ->count(),
            'active_field_officers' => User::where('role', 'field_officer')
                ->where('is_active', true)
                ->count(),
            'active_managers' => User::where('role', 'manager')
                ->where('is_active', true)
                ->count(),
            'new_registrations_today' => Member::whereDate('created_at', today())->count(),
            'collections_today' => Installment::whereDate('collection_date', today())
                ->where('status', 'paid')
                ->count(),
        ];
    }

    public function loadCriticalAlerts()
    {
        $this->criticalAlerts = [
            'overdue_installments' => Installment::where('status', 'overdue')->count(),
            'pending_approvals' => LoanApplication::where('status', 'pending')->count(),
            'inactive_users' => User::where('is_active', false)->count(),
            'high_default_branches' => Branch::whereHas('members.loanApplications.loan.installments', function($q) {
                $q->where('status', 'overdue');
            })->count(),
            'system_health' => 'Good', // This would be calculated based on various system metrics
        ];
    }

    public function loadGrowthMetrics()
    {
        $currentPeriod = Carbon::now()->subDays($this->selectedPeriod);
        $previousPeriod = Carbon::now()->subDays($this->selectedPeriod * 2);
        
        $currentMembers = Member::where('created_at', '>=', $currentPeriod)->count();
        $previousMembers = Member::where('created_at', '>=', $previousPeriod)
            ->where('created_at', '<', $currentPeriod)->count();
        
        $currentLoans = Loan::where('loan_date', '>=', $currentPeriod)->count();
        $previousLoans = Loan::where('loan_date', '>=', $previousPeriod)
            ->where('loan_date', '<', $currentPeriod)->count();
        
        $this->growthMetrics = [
            'member_growth' => $this->calculateGrowthRate($currentMembers, $previousMembers),
            'loan_growth' => $this->calculateGrowthRate($currentLoans, $previousLoans),
            'collection_growth' => $this->calculateCollectionGrowth($currentPeriod, $previousPeriod),
            'branch_expansion' => Branch::where('created_at', '>=', $currentPeriod)->count(),
        ];
    }

    public function loadInstallmentTrends()
    {
        $days = min(30, $this->selectedPeriod);
        $trends = [];
        
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $collected = Installment::whereDate('collection_date', $date)
                ->where('status', 'paid')
                ->sum('installment_amount');
            
            $trends[] = [
                'date' => $date->format('M d'),
                'amount' => $collected,
            ];
        }
        
        $this->installmentTrends = $trends;
    }

    public function loadDefaultRates()
    {
        $branches = Branch::all();
        $this->defaultRates = $branches->map(function($branch) {
            $totalInstallments = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->count();
            
            $overdueInstallments = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->where('status', 'overdue')->count();
            
            return [
                'branch' => $branch->name,
                'rate' => $totalInstallments > 0 ? round(($overdueInstallments / $totalInstallments) * 100, 2) : 0,
            ];
        })->toArray();
    }

    private function calculateOutstandingAmount()
    {
        return Installment::where('status', 'pending')
            ->orWhere('status', 'overdue')
            ->sum('installment_amount');
    }

    private function calculateCollectionEfficiency($startDate)
    {
        $totalDue = Installment::where('installment_date', '>=', $startDate)
            ->where('installment_date', '<=', now())
            ->sum('installment_amount');
            
        $totalCollected = Installment::where('collection_date', '>=', $startDate)
            ->where('status', 'paid')
            ->sum('installment_amount');
            
        return $totalDue > 0 ? round(($totalCollected / $totalDue) * 100, 2) : 0;
    }

    private function calculateDefaultRate()
    {
        $totalInstallments = Installment::count();
        $overdueInstallments = Installment::where('status', 'overdue')->count();
        
        return $totalInstallments > 0 ? round(($overdueInstallments / $totalInstallments) * 100, 2) : 0;
    }

    private function calculateProfitMargin($startDate)
    {
        $totalDisbursed = Loan::where('loan_date', '>=', $startDate)->sum('loan_amount');
        $totalRepayment = Loan::where('loan_date', '>=', $startDate)->sum('total_repayment_amount');
        
        return $totalDisbursed > 0 ? round((($totalRepayment - $totalDisbursed) / $totalDisbursed) * 100, 2) : 0;
    }

    private function calculateGrowthRate($current, $previous)
    {
        if ($previous == 0) return $current > 0 ? 100 : 0;
        return round((($current - $previous) / $previous) * 100, 2);
    }

    private function calculateCollectionGrowth($currentPeriod, $previousPeriod)
    {
        $currentCollections = Installment::where('collection_date', '>=', $currentPeriod)
            ->where('status', 'paid')
            ->sum('installment_amount');
            
        $previousCollections = Installment::where('collection_date', '>=', $previousPeriod)
            ->where('collection_date', '<', $currentPeriod)
            ->where('status', 'paid')
            ->sum('installment_amount');
            
        return $this->calculateGrowthRate($currentCollections, $previousCollections);
    }

    public function getBranches()
    {
        return Branch::all();
    }

    public function exportReport()
    {
        // Implementation for exporting comprehensive admin report
        session()->flash('success', 'Admin report export functionality will be implemented.');
    }

    public function render()
    {
        return view('livewire.admin-dashboard', [
            'branches' => $this->getBranches(),
        ]);
    }
}
