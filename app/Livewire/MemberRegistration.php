<?php

namespace App\Livewire;

use App\Models\Branch;
use App\Models\Member;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Validate;

class MemberRegistration extends Component
{
    use WithFileUploads;

    // Multi-step form state
    public $currentStep = 1;
    public $totalSteps = 4;

    // Step 1: Basic Information
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|string|max:255')]
    public $father_or_husband_name = '';

    #[Validate('required|string|max:255')]
    public $mother_name = '';

    #[Validate('required|date|before:today')]
    public $date_of_birth = '';

    #[Validate('required|string|max:100')]
    public $religion = '';

    #[Validate('required|string|max:20')]
    public $phone_number = '';

    #[Validate('nullable|string|max:10')]
    public $blood_group = '';

    // Step 2: Address Information
    #[Validate('required|string')]
    public $present_address = '';

    #[Validate('required|string')]
    public $permanent_address = '';

    #[Validate('required|string|size:13|unique:members,nid_number')]
    public $nid_number = '';

    #[Validate('required|string|max:255')]
    public $occupation = '';

    // Step 3: Photo and Reference
    #[Validate('nullable|image|max:2048')]
    public $photo;

    #[Validate('nullable|exists:members,id')]
    public $reference_id = '';

    // Step 4: Branch Assignment
    #[Validate('required|exists:branches,id')]
    public $branch_id = '';

    // Auto-save functionality
    public $autoSaveData = [];

    protected $listeners = ['autoSave'];

    public function mount()
    {
        // Load auto-saved data if exists
        $this->loadAutoSavedData();
        
        // Set default branch for field officers
        if (Auth::user()->isFieldOfficer() && Auth::user()->branch_id) {
            $this->branch_id = Auth::user()->branch_id;
        }
    }

    public function updatedName()
    {
        $this->autoSave();
    }

    public function updatedFatherOrHusbandName()
    {
        $this->autoSave();
    }

    public function updatedMotherName()
    {
        $this->autoSave();
    }

    public function updatedDateOfBirth()
    {
        $this->autoSave();
    }

    public function updatedReligion()
    {
        $this->autoSave();
    }

    public function updatedPhoneNumber()
    {
        $this->autoSave();
    }

    public function updatedBloodGroup()
    {
        $this->autoSave();
    }

    public function updatedPresentAddress()
    {
        $this->autoSave();
    }

    public function updatedPermanentAddress()
    {
        $this->autoSave();
    }

    public function updatedNidNumber()
    {
        $this->autoSave();
    }

    public function updatedOccupation()
    {
        $this->autoSave();
    }

    public function updatedReferenceId()
    {
        $this->autoSave();
    }

    public function updatedBranchId()
    {
        $this->autoSave();
    }

    public function autoSave()
    {
        $this->autoSaveData = [
            'name' => $this->name,
            'father_or_husband_name' => $this->father_or_husband_name,
            'mother_name' => $this->mother_name,
            'date_of_birth' => $this->date_of_birth,
            'religion' => $this->religion,
            'phone_number' => $this->phone_number,
            'blood_group' => $this->blood_group,
            'present_address' => $this->present_address,
            'permanent_address' => $this->permanent_address,
            'nid_number' => $this->nid_number,
            'occupation' => $this->occupation,
            'reference_id' => $this->reference_id,
            'branch_id' => $this->branch_id,
        ];

        session(['member_registration_autosave' => $this->autoSaveData]);
    }

    public function loadAutoSavedData()
    {
        $savedData = session('member_registration_autosave', []);
        
        foreach ($savedData as $key => $value) {
            if (property_exists($this, $key)) {
                $this->$key = $value;
            }
        }
    }

    public function clearAutoSavedData()
    {
        session()->forget('member_registration_autosave');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        
        if ($this->currentStep < $this->totalSteps) {
            $this->currentStep++;
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    public function goToStep($step)
    {
        if ($step >= 1 && $step <= $this->totalSteps) {
            $this->currentStep = $step;
        }
    }

    private function validateCurrentStep()
    {
        switch ($this->currentStep) {
            case 1:
                $this->validate([
                    'name' => 'required|string|max:255',
                    'father_or_husband_name' => 'required|string|max:255',
                    'mother_name' => 'required|string|max:255',
                    'date_of_birth' => 'required|date|before:today',
                    'religion' => 'required|string|max:100',
                    'phone_number' => 'required|string|max:20',
                    'blood_group' => 'nullable|string|max:10',
                ]);
                break;
            case 2:
                $this->validate([
                    'present_address' => 'required|string',
                    'permanent_address' => 'required|string',
                    'nid_number' => 'required|string|size:13|unique:members,nid_number',
                    'occupation' => 'required|string|max:255',
                ]);
                break;
            case 3:
                $this->validate([
                    'photo' => 'nullable|image|max:2048',
                    'reference_id' => 'nullable|exists:members,id',
                ]);
                break;
            case 4:
                $this->validate([
                    'branch_id' => 'required|exists:branches,id',
                ]);
                break;
        }
    }

    public function submit()
    {
        // Validate all steps
        $this->validate([
            'name' => 'required|string|max:255',
            'father_or_husband_name' => 'required|string|max:255',
            'mother_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'religion' => 'required|string|max:100',
            'phone_number' => 'required|string|max:20',
            'blood_group' => 'nullable|string|max:10',
            'present_address' => 'required|string',
            'permanent_address' => 'required|string',
            'nid_number' => 'required|string|size:13|unique:members,nid_number',
            'occupation' => 'required|string|max:255',
            'photo' => 'nullable|image|max:2048',
            'reference_id' => 'nullable|exists:members,id',
            'branch_id' => 'required|exists:branches,id',
        ]);

        // Handle photo upload
        $photoPath = null;
        if ($this->photo) {
            $photoPath = $this->photo->store('member-photos', 'public');
        }

        // Generate member ID
        $memberId = Member::generateMemberId($this->branch_id);

        // Create member
        $member = Member::create([
            'member_id' => $memberId,
            'name' => $this->name,
            'father_or_husband_name' => $this->father_or_husband_name,
            'mother_name' => $this->mother_name,
            'date_of_birth' => $this->date_of_birth,
            'religion' => $this->religion,
            'phone_number' => $this->phone_number,
            'blood_group' => $this->blood_group,
            'present_address' => $this->present_address,
            'permanent_address' => $this->permanent_address,
            'nid_number' => $this->nid_number,
            'occupation' => $this->occupation,
            'photo' => $photoPath,
            'reference_id' => $this->reference_id ?: null,
            'branch_id' => $this->branch_id,
            'created_by' => Auth::id(),
            'is_active' => true,
        ]);

        // Clear auto-saved data
        $this->clearAutoSavedData();

        session()->flash('message', 'Member registered successfully! Member ID: ' . $memberId);
        
        // Reset form
        $this->reset();
        $this->currentStep = 1;
        
        // Redirect to member management
        return $this->redirect(route('members.index'), navigate: true);
    }

    public function render()
    {
        return view('livewire.member-registration', [
            'branches' => Branch::all(),
            'members' => Member::active()->get(),
            'bloodGroups' => ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
            'religions' => ['Islam', 'Hinduism', 'Christianity', 'Buddhism', 'Others'],
        ]);
    }
}
