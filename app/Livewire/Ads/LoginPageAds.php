<?php

namespace App\Livewire\Ads;

use App\Models\Advertisement;
use Livewire\Component;
use Illuminate\Support\Collection;

class LoginPageAds extends Component
{
    public $currentAdIndex = 0;
    public $ads;
    public $autoRotate = true;
    public $rotationInterval = 5000; // 5 seconds

    public function mount($placement = 'login_page')
    {
        $this->ads = Advertisement::active()
            ->scheduled()
            ->placement($placement)
            ->orderBy('display_order')
            ->get();

        // Record impressions for all ads
        foreach ($this->ads as $ad) {
            $ad->recordImpression();
        }
    }

    public function render()
    {
        return view('livewire.ads.login-page-ads');
    }

    public function nextAd()
    {
        if ($this->ads->count() > 1) {
            $this->currentAdIndex = ($this->currentAdIndex + 1) % $this->ads->count();
        }
    }

    public function previousAd()
    {
        if ($this->ads->count() > 1) {
            $this->currentAdIndex = $this->currentAdIndex > 0
                ? $this->currentAdIndex - 1
                : $this->ads->count() - 1;
        }
    }

    public function selectAd($index)
    {
        if ($index >= 0 && $index < $this->ads->count()) {
            $this->currentAdIndex = $index;
        }
    }

    public function clickAd($adId)
    {
        $ad = Advertisement::find($adId);
        if ($ad) {
            $ad->recordClick();

            if ($ad->link_url) {
                $this->dispatch('redirect', ['url' => $ad->link_url]);
            }
        }
    }

    public function getCurrentAd()
    {
        return $this->ads->get($this->currentAdIndex);
    }

    public function hasMultipleAds(): bool
    {
        return $this->ads->count() > 1;
    }

    public function getAdAnalytics(): array
    {
        if ($this->ads->isEmpty()) {
            return [];
        }

        return [
            'total_ads' => $this->ads->count(),
            'total_impressions' => $this->ads->sum('impression_count'),
            'total_clicks' => $this->ads->sum('click_count'),
            'average_ctr' => $this->ads->avg('click_through_rate'),
        ];
    }

    public function toggleAutoRotate()
    {
        $this->autoRotate = !$this->autoRotate;
    }

    public function setRotationInterval($interval)
    {
        $this->rotationInterval = max(1000, min(10000, $interval)); // Between 1-10 seconds
    }
}
