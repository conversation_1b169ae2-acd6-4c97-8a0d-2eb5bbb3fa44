<?php

namespace App\Livewire;

use App\Models\LoanApplication;
use App\Models\Loan;
use App\Models\Installment;
use App\Services\LoanCalculatorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class LoanApproval extends Component
{
    use WithPagination;

    // Filters
    public $status_filter = 'pending';
    public $search = '';
    public $branch_filter = '';
    public $date_from = '';
    public $date_to = '';
    
    // Bulk actions
    public $selected_applications = [];
    public $select_all = false;
    
    // Review modal
    public $show_review_modal = false;
    public $reviewing_application = null;
    public $review_action = '';
    public $review_comments = '';
    public $loan_amount = '';
    public $repayment_method = 'monthly';
    public $duration_months = 12;
    public $interest_rate = 15;
    public $advance_payment = 0;
    public $loan_calculation = [];

    protected $rules = [
        'review_comments' => 'required|string|min:10|max:500',
        'loan_amount' => 'required_if:review_action,approve|numeric|min:1000|max:500000',
        'repayment_method' => 'required_if:review_action,approve|in:weekly,monthly',
        'duration_months' => 'required_if:review_action,approve|integer|min:3|max:60',
        'advance_payment' => 'nullable|numeric|min:0',
    ];

    public function mount()
    {
        $this->date_from = Carbon::now()->subMonth()->format('Y-m-d');
        $this->date_to = Carbon::now()->format('Y-m-d');
    }

    public function updatedSelectAll()
    {
        if ($this->select_all) {
            $this->selected_applications = $this->getApplicationsQuery()->pluck('id')->toArray();
        } else {
            $this->selected_applications = [];
        }
    }

    public function updatedSelectedApplications()
    {
        $this->select_all = count($this->selected_applications) === $this->getApplicationsQuery()->count();
    }

    public function getApplicationsQuery()
    {
        $query = LoanApplication::with(['member', 'reviewer'])
            ->when($this->status_filter, function ($q) {
                $q->where('status', $this->status_filter);
            })
            ->when($this->search, function ($q) {
                $q->whereHas('member', function ($memberQuery) {
                    $memberQuery->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('member_id', 'like', '%' . $this->search . '%')
                        ->orWhere('phone_number', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->date_from, function ($q) {
                $q->whereDate('applied_at', '>=', $this->date_from);
            })
            ->when($this->date_to, function ($q) {
                $q->whereDate('applied_at', '<=', $this->date_to);
            });

        // Branch filter for managers
        if (Auth::user()->role === 'manager') {
            $query->whereHas('member', function ($memberQuery) {
                $memberQuery->where('branch_id', Auth::user()->branch_id);
            });
        } elseif ($this->branch_filter) {
            $query->whereHas('member', function ($memberQuery) {
                $memberQuery->where('branch_id', $this->branch_filter);
            });
        }

        return $query->orderBy('applied_at', 'desc');
    }

    public function openReviewModal($applicationId, $action)
    {
        $this->reviewing_application = LoanApplication::with('member')->find($applicationId);
        $this->review_action = $action;
        $this->review_comments = '';
        
        if ($action === 'approve') {
            $this->loan_amount = $this->reviewing_application->applied_amount;
            $this->advance_payment = $this->reviewing_application->advance_payment;
            $this->calculateLoan();
        }
        
        $this->show_review_modal = true;
    }

    public function closeReviewModal()
    {
        $this->show_review_modal = false;
        $this->reviewing_application = null;
        $this->review_action = '';
        $this->review_comments = '';
        $this->loan_calculation = [];
        $this->resetValidation();
    }

    public function updatedLoanAmount()
    {
        if ($this->review_action === 'approve') {
            $this->calculateLoan();
        }
    }

    public function updatedAdvancePayment()
    {
        if ($this->review_action === 'approve') {
            $this->calculateLoan();
        }
    }

    public function updatedRepaymentMethod()
    {
        if ($this->review_action === 'approve') {
            $this->calculateLoan();
        }
    }

    public function updatedDurationMonths()
    {
        if ($this->review_action === 'approve') {
            $this->calculateLoan();
        }
    }

    public function calculateLoan()
    {
        if ($this->loan_amount && $this->loan_amount >= 1000) {
            $this->loan_calculation = LoanCalculatorService::calculateLoan(
                loanAmount: (float) $this->loan_amount,
                interestRate: $this->interest_rate,
                durationMonths: $this->duration_months,
                repaymentMethod: $this->repayment_method,
                advancePayment: (float) $this->advance_payment
            );
        }
    }

    public function submitReview()
    {
        $this->validate();

        DB::transaction(function () {
            $this->reviewing_application->update([
                'status' => $this->review_action === 'approve' ? 'approved' : 'rejected',
                'reviewed_by' => Auth::id(),
                'reviewed_at' => now(),
                'review_comments' => $this->review_comments,
            ]);

            if ($this->review_action === 'approve') {
                $this->createLoanAndInstallments();
            }
        });

        session()->flash('success', 'Application ' . $this->review_action . 'd successfully!');
        $this->closeReviewModal();
    }

    private function createLoanAndInstallments()
    {
        // Create loan record
        $loan = Loan::create([
            'loan_application_id' => $this->reviewing_application->id,
            'loan_date' => now()->toDateString(),
            'loan_amount' => $this->loan_amount,
            'total_repayment_amount' => $this->loan_calculation['total_repayment_amount'],
            'repayment_duration' => $this->duration_months . ' months',
            'repayment_method' => $this->repayment_method,
            'installment_count' => $this->loan_calculation['installment_count'],
            'installment_amount' => $this->loan_calculation['installment_amount'],
            'advance_payment' => $this->advance_payment,
            'first_installment_date' => $this->loan_calculation['first_installment_date'],
            'last_installment_date' => $this->loan_calculation['last_installment_date'],
        ]);

        // Create installment records
        foreach ($this->loan_calculation['payment_schedule'] as $installment) {
            Installment::create([
                'loan_id' => $loan->id,
                'installment_no' => $installment['installment_no'],
                'installment_date' => $installment['date'],
                'installment_amount' => $installment['amount'],
                'status' => 'pending',
            ]);
        }
    }

    public function bulkApprove()
    {
        if (empty($this->selected_applications)) {
            session()->flash('error', 'Please select applications to approve.');
            return;
        }

        $count = 0;
        foreach ($this->selected_applications as $applicationId) {
            $application = LoanApplication::find($applicationId);
            if ($application && $application->status === 'pending') {
                DB::transaction(function () use ($application) {
                    $application->update([
                        'status' => 'approved',
                        'reviewed_by' => Auth::id(),
                        'reviewed_at' => now(),
                        'review_comments' => 'Bulk approved',
                    ]);

                    // Create loan with default calculation
                    $calculation = LoanCalculatorService::calculateLoan(
                        loanAmount: $application->applied_amount,
                        interestRate: 15,
                        durationMonths: 12,
                        repaymentMethod: 'monthly',
                        advancePayment: $application->advance_payment
                    );

                    $loan = Loan::create([
                        'loan_application_id' => $application->id,
                        'loan_date' => now()->toDateString(),
                        'loan_amount' => $application->applied_amount,
                        'total_repayment_amount' => $calculation['total_repayment_amount'],
                        'repayment_duration' => '12 months',
                        'repayment_method' => 'monthly',
                        'installment_count' => $calculation['installment_count'],
                        'installment_amount' => $calculation['installment_amount'],
                        'advance_payment' => $application->advance_payment,
                        'first_installment_date' => $calculation['first_installment_date'],
                        'last_installment_date' => $calculation['last_installment_date'],
                    ]);

                    foreach ($calculation['payment_schedule'] as $installment) {
                        Installment::create([
                            'loan_id' => $loan->id,
                            'installment_no' => $installment['installment_no'],
                            'installment_date' => $installment['date'],
                            'installment_amount' => $installment['amount'],
                            'status' => 'pending',
                        ]);
                    }
                });
                $count++;
            }
        }

        session()->flash('success', "Successfully approved {$count} applications.");
        $this->selected_applications = [];
        $this->select_all = false;
    }

    public function render()
    {
        $applications = $this->getApplicationsQuery()->paginate(10);
        
        return view('livewire.loan-approval', [
            'applications' => $applications,
        ]);
    }
}
