<?php

namespace App\Livewire;

use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\SavingAccount;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class MemberDashboard extends Component
{
    use WithPagination;

    public $member;
    public $selectedTab = 'overview';

    public function mount()
    {
        // Get the member profile for the authenticated user
        $this->member = Member::where('id', Auth::user()->member_id)
            ->with([
                'branch',
                'activeLoans.installments',
                'activeSavingAccounts',
                'loanApplications' => function ($query) {
                    $query->latest()->take(5);
                }
            ])
            ->first();

        if (!$this->member) {
            abort(404, 'Member profile not found.');
        }
    }

    public function setTab($tab)
    {
        $this->selectedTab = $tab;
        $this->resetPage();
    }

    public function getAccountOverview()
    {
        return [
            'total_savings' => $this->member->total_savings,
            'total_loan_amount' => $this->member->total_loan_amount,
            'outstanding_balance' => $this->member->outstanding_loan_balance,
            'active_loans_count' => $this->member->loanApplications()->where('status', 'approved')->whereHas('loan')->count(),
            'savings_accounts_count' => $this->member->activeSavingAccounts->count(),
            'has_overdue' => $this->member->hasOverdueInstallments(),
        ];
    }

    public function getActiveLoans()
    {
        return $this->member->loanApplications()
            ->where('status', 'approved')
            ->whereHas('loan')
            ->with(['loan.installments' => function ($query) {
                $query->orderBy('installment_date');
            }])
            ->get()
            ->map(function ($application) {
                return $application->loan;
            })
            ->filter();
    }

    public function getUpcomingInstallments()
    {
        return Installment::whereHas('loan.loanApplication', function ($query) {
                $query->where('member_id', $this->member->id)
                      ->where('status', 'approved');
            })
            ->where('status', 'pending')
            ->where('installment_date', '>=', now()->toDateString())
            ->where('installment_date', '<=', now()->addDays(30)->toDateString())
            ->orderBy('installment_date')
            ->take(10)
            ->get();
    }

    public function getOverdueInstallments()
    {
        return Installment::whereHas('loan.loanApplication', function ($query) {
                $query->where('member_id', $this->member->id)
                      ->where('status', 'approved');
            })
            ->where('status', 'overdue')
            ->orderBy('installment_date')
            ->get();
    }

    public function getRecentTransactions()
    {
        // This would typically include loan disbursements, installment payments, savings deposits, etc.
        // For now, we'll show recent installment payments
        return Installment::whereHas('loan.loanApplication', function ($query) {
                $query->where('member_id', $this->member->id)
                      ->where('status', 'approved');
            })
            ->where('status', 'paid')
            ->with(['loan.loanApplication', 'collector'])
            ->orderBy('collection_date', 'desc')
            ->paginate(10);
    }

    public function getSavingsAccounts()
    {
        return $this->member->activeSavingAccounts()
            ->with(['creator'])
            ->get();
    }

    public function getLoanApplications()
    {
        return $this->member->loanApplications()
            ->with(['reviewer', 'loan'])
            ->orderBy('applied_at', 'desc')
            ->paginate(10);
    }

    public function render()
    {
        $data = [
            'accountOverview' => $this->getAccountOverview(),
            'activeLoans' => $this->getActiveLoans(),
            'upcomingInstallments' => $this->getUpcomingInstallments(),
            'overdueInstallments' => $this->getOverdueInstallments(),
            'savingsAccounts' => $this->getSavingsAccounts(),
        ];

        // Add paginated data based on selected tab
        switch ($this->selectedTab) {
            case 'transactions':
                $data['recentTransactions'] = $this->getRecentTransactions();
                break;
            case 'loans':
                $data['loanApplications'] = $this->getLoanApplications();
                break;
        }

        return view('livewire.member-dashboard', $data);
    }
}
