<?php

namespace App\Livewire\Reports;

use App\Models\Installment;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Member;
use App\Models\User;
use App\Models\Branch;
use App\Models\BranchTransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Carbon\Carbon;

class AnalyticsEngine extends Component
{
    public $analytics_type = 'installment_trends';
    public $date_from = '';
    public $date_to = '';
    public $branch_filter = '';
    public $officer_filter = '';

    public $analytics_data = [];
    public $chart_data = [];
    public $kpi_metrics = [];
    public $insights = [];

    public function mount()
    {
        $this->date_from = now()->subMonths(6)->format('Y-m-d');
        $this->date_to = now()->format('Y-m-d');
        $this->branch_filter = Auth::user()->branch_id ?? '';
    }

    public function render()
    {
        $branches = Branch::all();
        $officers = User::where('role', 'field_officer')
                       ->when($this->branch_filter, function($q) {
                           $q->where('branch_id', $this->branch_filter);
                       })
                       ->get();

        return view('livewire.reports.analytics-engine', [
            'branches' => $branches,
            'officers' => $officers,
        ]);
    }

    public function generateAnalytics()
    {
        switch ($this->analytics_type) {
            case 'installment_trends':
                $this->generateInstallmentTrends();
                break;
            case 'default_rates':
                $this->generateDefaultRateAnalysis();
                break;
            case 'portfolio_risk':
                $this->generatePortfolioRiskAssessment();
                break;
            case 'member_behavior':
                $this->generateMemberBehaviorAnalytics();
                break;
            case 'officer_performance':
                $this->generateOfficerPerformanceMetrics();
                break;
            case 'branch_profitability':
                $this->generateBranchProfitabilityAnalysis();
                break;
        }
    }

    private function generateInstallmentTrends()
    {
        $query = Installment::with(['loan.loanApplication.member'])
            ->whereBetween('installment_date', [$this->date_from, $this->date_to]);

        $this->applyFilters($query);

        $installments = $query->get();

        // Monthly trends
        $monthlyData = $installments->groupBy(function($item) {
            return $item->installment_date->format('Y-m');
        })->map(function($monthInstallments, $month) {
            $totalDue = $monthInstallments->sum('installment_amount');
            $totalCollected = $monthInstallments->where('status', 'paid')->sum('installment_amount');
            $overdueCount = $monthInstallments->where('status', 'overdue')->count();

            return [
                'month' => Carbon::createFromFormat('Y-m', $month)->format('M Y'),
                'total_due' => $totalDue,
                'total_collected' => $totalCollected,
                'collection_rate' => $totalDue > 0 ? ($totalCollected / $totalDue) * 100 : 0,
                'overdue_count' => $overdueCount,
                'total_count' => $monthInstallments->count(),
            ];
        })->sortBy('month');

        $this->analytics_data = $monthlyData->values()->toArray();

        // KPI Metrics
        $this->kpi_metrics = [
            'total_installments' => $installments->count(),
            'total_amount_due' => $installments->sum('installment_amount'),
            'total_collected' => $installments->where('status', 'paid')->sum('installment_amount'),
            'overall_collection_rate' => $installments->sum('installment_amount') > 0 ?
                ($installments->where('status', 'paid')->sum('installment_amount') / $installments->sum('installment_amount')) * 100 : 0,
            'overdue_percentage' => $installments->count() > 0 ?
                ($installments->where('status', 'overdue')->count() / $installments->count()) * 100 : 0,
        ];

        // Chart data
        $this->chart_data = [
            'labels' => $monthlyData->pluck('month')->toArray(),
            'collection_rates' => $monthlyData->pluck('collection_rate')->toArray(),
            'amounts_due' => $monthlyData->pluck('total_due')->toArray(),
            'amounts_collected' => $monthlyData->pluck('total_collected')->toArray(),
        ];

        // Generate insights
        $this->generateInstallmentInsights($monthlyData);
    }

    private function generateDefaultRateAnalysis()
    {
        $query = Loan::with(['loanApplication.member', 'installments'])
            ->whereHas('loanApplication', function($q) {
                $q->where('status', 'approved');
            })
            ->whereBetween('loan_date', [$this->date_from, $this->date_to]);

        $this->applyLoanFilters($query);

        $loans = $query->get();

        $defaultAnalysis = [];
        $totalLoans = $loans->count();
        $defaultedLoans = 0;
        $atRiskLoans = 0;

        foreach ($loans as $loan) {
            $overdueInstallments = $loan->installments->where('status', 'overdue')->count();
            $totalInstallments = $loan->installments->count();

            $overduePercentage = $totalInstallments > 0 ? ($overdueInstallments / $totalInstallments) * 100 : 0;

            if ($overduePercentage >= 50) {
                $defaultedLoans++;
                $riskLevel = 'High Risk';
            } elseif ($overduePercentage >= 25) {
                $atRiskLoans++;
                $riskLevel = 'Medium Risk';
            } else {
                $riskLevel = 'Low Risk';
            }

            $defaultAnalysis[] = [
                'loan_id' => $loan->id,
                'member_name' => $loan->loanApplication->member->name,
                'loan_amount' => $loan->loan_amount,
                'overdue_percentage' => $overduePercentage,
                'risk_level' => $riskLevel,
                'overdue_amount' => $loan->installments->where('status', 'overdue')->sum('installment_amount'),
            ];
        }

        $this->analytics_data = $defaultAnalysis;

        $this->kpi_metrics = [
            'total_loans' => $totalLoans,
            'defaulted_loans' => $defaultedLoans,
            'at_risk_loans' => $atRiskLoans,
            'default_rate' => $totalLoans > 0 ? ($defaultedLoans / $totalLoans) * 100 : 0,
            'risk_rate' => $totalLoans > 0 ? (($defaultedLoans + $atRiskLoans) / $totalLoans) * 100 : 0,
        ];

        // Risk distribution for chart
        $riskDistribution = collect($defaultAnalysis)->groupBy('risk_level')->map->count();

        $this->chart_data = [
            'labels' => $riskDistribution->keys()->toArray(),
            'values' => $riskDistribution->values()->toArray(),
        ];

        $this->generateDefaultRateInsights();
    }

    private function generatePortfolioRiskAssessment()
    {
        $query = Loan::with(['loanApplication.member.branch', 'installments'])
            ->whereHas('loanApplication', function($q) {
                $q->where('status', 'approved');
            });

        $this->applyLoanFilters($query);

        $loans = $query->get();

        $portfolioData = [];
        $totalPortfolioValue = $loans->sum('loan_amount');
        $totalOutstanding = 0;
        $totalOverdue = 0;

        // Risk categorization by loan amount
        $riskCategories = [
            'low' => ['min' => 0, 'max' => 50000, 'loans' => 0, 'amount' => 0],
            'medium' => ['min' => 50001, 'max' => 200000, 'loans' => 0, 'amount' => 0],
            'high' => ['min' => 200001, 'max' => PHP_INT_MAX, 'loans' => 0, 'amount' => 0],
        ];

        foreach ($loans as $loan) {
            $outstandingAmount = $loan->installments->where('status', '!=', 'paid')->sum('installment_amount');
            $overdueAmount = $loan->installments->where('status', 'overdue')->sum('installment_amount');

            $totalOutstanding += $outstandingAmount;
            $totalOverdue += $overdueAmount;

            // Categorize by loan amount
            foreach ($riskCategories as $category => &$data) {
                if ($loan->loan_amount >= $data['min'] && $loan->loan_amount <= $data['max']) {
                    $data['loans']++;
                    $data['amount'] += $loan->loan_amount;
                    break;
                }
            }

            $portfolioData[] = [
                'loan_id' => $loan->id,
                'member_name' => $loan->loanApplication->member->name,
                'branch_name' => $loan->loanApplication->member->branch->name,
                'loan_amount' => $loan->loan_amount,
                'outstanding_amount' => $outstandingAmount,
                'overdue_amount' => $overdueAmount,
                'risk_score' => $this->calculateRiskScore($loan),
            ];
        }

        $this->analytics_data = $portfolioData;

        $this->kpi_metrics = [
            'total_portfolio_value' => $totalPortfolioValue,
            'total_outstanding' => $totalOutstanding,
            'total_overdue' => $totalOverdue,
            'portfolio_at_risk' => $totalPortfolioValue > 0 ? ($totalOverdue / $totalPortfolioValue) * 100 : 0,
            'recovery_rate' => $totalPortfolioValue > 0 ? (($totalPortfolioValue - $totalOutstanding) / $totalPortfolioValue) * 100 : 0,
        ];

        $this->chart_data = [
            'risk_categories' => $riskCategories,
            'portfolio_distribution' => [
                'labels' => ['Outstanding', 'Overdue', 'Recovered'],
                'values' => [$totalOutstanding - $totalOverdue, $totalOverdue, $totalPortfolioValue - $totalOutstanding],
            ],
        ];

        $this->generatePortfolioInsights();
    }

    private function generateOfficerPerformanceMetrics()
    {
        $query = User::where('role', 'field_officer')
            ->with(['memberProfile.branch']);

        if ($this->branch_filter) {
            $query->where('branch_id', $this->branch_filter);
        }

        if ($this->officer_filter) {
            $query->where('id', $this->officer_filter);
        }

        $officers = $query->get();
        $performanceData = [];

        foreach ($officers as $officer) {
            // Collections in period
            $collections = Installment::where('collected_by', $officer->id)
                ->whereBetween('collection_date', [$this->date_from, $this->date_to])
                ->where('status', 'paid');

            $totalCollected = $collections->sum('installment_amount');
            $collectionsCount = $collections->count();

            // Members managed
            $membersManaged = Member::where('branch_id', $officer->branch_id)
                ->where('is_active', true)
                ->count();

            // New members registered
            $newMembers = Member::where('created_by', $officer->id)
                ->whereBetween('created_at', [$this->date_from, $this->date_to])
                ->count();

            // Efficiency calculation
            $dueInstallments = Installment::whereHas('loan.loanApplication.member', function($q) use ($officer) {
                $q->where('branch_id', $officer->branch_id);
            })->whereBetween('installment_date', [$this->date_from, $this->date_to]);

            $totalDue = $dueInstallments->sum('installment_amount');
            $efficiency = $totalDue > 0 ? ($totalCollected / $totalDue) * 100 : 0;

            $performanceData[] = [
                'officer_id' => $officer->id,
                'officer_name' => $officer->name,
                'branch_name' => $officer->branch->name ?? 'N/A',
                'total_collected' => $totalCollected,
                'collections_count' => $collectionsCount,
                'members_managed' => $membersManaged,
                'new_members' => $newMembers,
                'efficiency_rate' => $efficiency,
                'avg_collection' => $collectionsCount > 0 ? $totalCollected / $collectionsCount : 0,
            ];
        }

        // Sort by efficiency
        usort($performanceData, function($a, $b) {
            return $b['efficiency_rate'] <=> $a['efficiency_rate'];
        });

        $this->analytics_data = $performanceData;

        $this->kpi_metrics = [
            'total_officers' => count($performanceData),
            'avg_efficiency' => collect($performanceData)->avg('efficiency_rate'),
            'top_performer' => $performanceData[0]['officer_name'] ?? 'N/A',
            'total_collections' => collect($performanceData)->sum('total_collected'),
            'total_new_members' => collect($performanceData)->sum('new_members'),
        ];

        $this->chart_data = [
            'labels' => collect($performanceData)->pluck('officer_name')->toArray(),
            'efficiency_rates' => collect($performanceData)->pluck('efficiency_rate')->toArray(),
            'collections' => collect($performanceData)->pluck('total_collected')->toArray(),
        ];

        $this->generateOfficerInsights($performanceData);
    }

    private function calculateRiskScore(Loan $loan): float
    {
        $overdueInstallments = $loan->installments->where('status', 'overdue')->count();
        $totalInstallments = $loan->installments->count();
        $overdueAmount = $loan->installments->where('status', 'overdue')->sum('installment_amount');

        $overduePercentage = $totalInstallments > 0 ? ($overdueInstallments / $totalInstallments) * 100 : 0;
        $amountRisk = $loan->loan_amount > 100000 ? 20 : 0;

        return min(100, $overduePercentage + $amountRisk);
    }

    private function applyFilters($query)
    {
        if ($this->branch_filter) {
            $query->whereHas('loan.loanApplication.member', function($q) {
                $q->where('branch_id', $this->branch_filter);
            });
        }

        if ($this->officer_filter) {
            $query->where('collected_by', $this->officer_filter);
        }
    }

    private function applyLoanFilters($query)
    {
        if ($this->branch_filter) {
            $query->whereHas('loanApplication.member', function($q) {
                $q->where('branch_id', $this->branch_filter);
            });
        }
    }

    private function generateInstallmentInsights($monthlyData)
    {
        $this->insights = [];

        $avgCollectionRate = $monthlyData->avg('collection_rate');
        $trend = $this->calculateTrend($monthlyData->pluck('collection_rate')->toArray());

        if ($avgCollectionRate < 80) {
            $this->insights[] = [
                'type' => 'warning',
                'title' => 'Low Collection Rate',
                'message' => 'Average collection rate is ' . round($avgCollectionRate, 1) . '%. Consider reviewing collection strategies.',
            ];
        }

        if ($trend === 'declining') {
            $this->insights[] = [
                'type' => 'danger',
                'title' => 'Declining Trend',
                'message' => 'Collection rates are showing a declining trend. Immediate action required.',
            ];
        } elseif ($trend === 'improving') {
            $this->insights[] = [
                'type' => 'success',
                'title' => 'Improving Performance',
                'message' => 'Collection rates are improving. Keep up the good work!',
            ];
        }
    }

    private function generateDefaultRateInsights()
    {
        $defaultRate = $this->kpi_metrics['default_rate'];

        if ($defaultRate > 10) {
            $this->insights[] = [
                'type' => 'danger',
                'title' => 'High Default Rate',
                'message' => 'Default rate of ' . round($defaultRate, 1) . '% is above acceptable threshold.',
            ];
        }
    }

    private function generatePortfolioInsights()
    {
        $portfolioAtRisk = $this->kpi_metrics['portfolio_at_risk'];

        if ($portfolioAtRisk > 5) {
            $this->insights[] = [
                'type' => 'warning',
                'title' => 'Portfolio at Risk',
                'message' => round($portfolioAtRisk, 1) . '% of portfolio is at risk. Monitor closely.',
            ];
        }
    }

    private function generateOfficerInsights($performanceData)
    {
        $lowPerformers = collect($performanceData)->where('efficiency_rate', '<', 70)->count();

        if ($lowPerformers > 0) {
            $this->insights[] = [
                'type' => 'warning',
                'title' => 'Performance Issues',
                'message' => $lowPerformers . ' officer(s) have efficiency below 70%. Training may be needed.',
            ];
        }
    }

    private function calculateTrend(array $values): string
    {
        if (count($values) < 2) return 'stable';

        $firstHalf = array_slice($values, 0, ceil(count($values) / 2));
        $secondHalf = array_slice($values, floor(count($values) / 2));

        $firstAvg = array_sum($firstHalf) / count($firstHalf);
        $secondAvg = array_sum($secondHalf) / count($secondHalf);

        if ($secondAvg > $firstAvg * 1.05) return 'improving';
        if ($secondAvg < $firstAvg * 0.95) return 'declining';
        return 'stable';
    }

    public function exportAnalytics()
    {
        // Implementation for exporting analytics data
        session()->flash('info', 'Analytics export functionality will be implemented soon.');
    }

    public function clearFilters()
    {
        $this->branch_filter = Auth::user()->branch_id ?? '';
        $this->officer_filter = '';
        $this->analytics_data = [];
        $this->chart_data = [];
        $this->kpi_metrics = [];
        $this->insights = [];
    }
}
