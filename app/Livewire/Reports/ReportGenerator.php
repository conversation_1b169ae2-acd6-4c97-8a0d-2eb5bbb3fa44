<?php

namespace App\Livewire\Reports;

use App\Models\ReportTemplate;
use App\Models\ScheduledReport;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class ReportGenerator extends Component
{
    use WithPagination;

    // Template management
    public $showTemplateModal = false;
    public $editingTemplate = null;
    public $template_name = '';
    public $template_type = 'financial';
    public $template_description = '';
    public $template_configuration = [];
    public $template_filters = [];
    public $export_format = 'pdf';
    public $is_public = false;

    // Report generation
    public $selected_template_id = '';
    public $custom_filters = [];
    public $date_from = '';
    public $date_to = '';
    public $branch_filter = '';
    public $user_filter = '';

    // Scheduling
    public $showScheduleModal = false;
    public $schedule_name = '';
    public $schedule_frequency = 'monthly';
    public $schedule_time = '09:00';
    public $schedule_day = 1;
    public $recipients = '';
    public $schedule_filters = [];

    // Report data
    public $generated_report = [];
    public $report_summary = [];

    // Filters
    public $filter_type = '';
    public $search = '';

    protected $rules = [
        'template_name' => 'required|string|max:255',
        'template_type' => 'required|string',
        'template_description' => 'nullable|string|max:500',
        'export_format' => 'required|in:pdf,excel,csv',
        'schedule_name' => 'required|string|max:255',
        'schedule_frequency' => 'required|in:daily,weekly,monthly,quarterly,yearly',
        'schedule_time' => 'required',
        'recipients' => 'required|string',
    ];

    public function mount()
    {
        $this->date_from = now()->startOfMonth()->format('Y-m-d');
        $this->date_to = now()->format('Y-m-d');
    }

    public function render()
    {
        $templates = $this->getTemplates();
        $scheduledReports = $this->getScheduledReports();
        $branches = Branch::all();
        $users = User::where('role', '!=', 'member')->get();

        return view('livewire.reports.report-generator', [
            'templates' => $templates,
            'scheduledReports' => $scheduledReports,
            'branches' => $branches,
            'users' => $users,
            'reportTypes' => ReportTemplate::getReportTypes(),
            'exportFormats' => ReportTemplate::getExportFormats(),
            'frequencies' => ScheduledReport::getFrequencies(),
        ]);
    }

    private function getTemplates()
    {
        $query = ReportTemplate::with('creator')
            ->where(function($q) {
                $q->where('is_public', true)
                  ->orWhere('created_by', Auth::id());
            });

        if ($this->filter_type) {
            $query->where('type', $this->filter_type);
        }

        if ($this->search) {
            $query->where('name', 'like', '%' . $this->search . '%');
        }

        return $query->active()->orderBy('created_at', 'desc')->paginate(10);
    }

    private function getScheduledReports()
    {
        return ScheduledReport::with(['template', 'creator'])
            ->where('created_by', Auth::id())
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
    }

    public function openTemplateModal()
    {
        $this->resetTemplateForm();
        $this->showTemplateModal = true;
    }

    public function closeTemplateModal()
    {
        $this->showTemplateModal = false;
        $this->editingTemplate = null;
        $this->resetTemplateForm();
    }

    public function editTemplate($templateId)
    {
        $template = ReportTemplate::findOrFail($templateId);

        if (!$template->canBeEditedBy(Auth::user())) {
            session()->flash('error', 'You do not have permission to edit this template.');
            return;
        }

        $this->editingTemplate = $template;
        $this->template_name = $template->name;
        $this->template_type = $template->type;
        $this->template_description = $template->description;
        $this->template_configuration = $template->configuration ?? [];
        $this->template_filters = $template->default_filters ?? [];
        $this->export_format = $template->export_format;
        $this->is_public = $template->is_public;

        $this->showTemplateModal = true;
    }

    public function saveTemplate()
    {
        $this->validate([
            'template_name' => 'required|string|max:255',
            'template_type' => 'required|string',
            'template_description' => 'nullable|string|max:500',
            'export_format' => 'required|in:pdf,excel,csv',
        ]);

        DB::transaction(function () {
            $data = [
                'name' => $this->template_name,
                'type' => $this->template_type,
                'description' => $this->template_description,
                'configuration' => $this->template_configuration,
                'default_filters' => $this->template_filters,
                'export_format' => $this->export_format,
                'is_public' => $this->is_public,
                'created_by' => Auth::id(),
            ];

            if ($this->editingTemplate) {
                $this->editingTemplate->update($data);
                session()->flash('success', 'Report template updated successfully!');
            } else {
                ReportTemplate::create($data);
                session()->flash('success', 'Report template created successfully!');
            }
        });

        $this->closeTemplateModal();
    }

    public function generateReport()
    {
        if (!$this->selected_template_id) {
            session()->flash('error', 'Please select a report template.');
            return;
        }

        $template = ReportTemplate::findOrFail($this->selected_template_id);

        $filters = array_merge($this->custom_filters, [
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'branch_id' => $this->branch_filter,
            'user_id' => $this->user_filter,
        ]);

        try {
            $this->generated_report = $template->generateReport($filters);
            $this->report_summary = $this->calculateReportSummary();

            session()->flash('success', 'Report generated successfully!');
        } catch (\Exception $e) {
            session()->flash('error', 'Error generating report: ' . $e->getMessage());
        }
    }

    public function exportReport($format = null)
    {
        if (empty($this->generated_report)) {
            session()->flash('error', 'Please generate a report first.');
            return;
        }

        $template = ReportTemplate::findOrFail($this->selected_template_id);
        $exportFormat = $format ?? $template->export_format;

        try {
            // This would implement actual export functionality
            $filename = $this->generateExportFile($exportFormat);

            session()->flash('success', "Report exported as {$exportFormat}. Download will start shortly.");

            // Return download response
            return response()->download($filename);
        } catch (\Exception $e) {
            session()->flash('error', 'Error exporting report: ' . $e->getMessage());
        }
    }

    public function openScheduleModal()
    {
        if (!$this->selected_template_id) {
            session()->flash('error', 'Please select a report template first.');
            return;
        }

        $this->resetScheduleForm();
        $this->showScheduleModal = true;
    }

    public function closeScheduleModal()
    {
        $this->showScheduleModal = false;
        $this->resetScheduleForm();
    }

    public function scheduleReport()
    {
        $this->validate([
            'schedule_name' => 'required|string|max:255',
            'schedule_frequency' => 'required|in:daily,weekly,monthly,quarterly,yearly',
            'schedule_time' => 'required',
            'recipients' => 'required|string',
        ]);

        DB::transaction(function () {
            $recipients = array_map('trim', explode(',', $this->recipients));

            $nextRunDate = $this->calculateNextRunDate();

            ScheduledReport::create([
                'template_id' => $this->selected_template_id,
                'name' => $this->schedule_name,
                'frequency' => $this->schedule_frequency,
                'recipients' => $recipients,
                'filters' => array_merge($this->custom_filters, [
                    'branch_id' => $this->branch_filter,
                    'user_id' => $this->user_filter,
                ]),
                'schedule_time' => $this->schedule_time,
                'schedule_day' => $this->schedule_day,
                'next_run_date' => $nextRunDate,
                'created_by' => Auth::id(),
            ]);

            session()->flash('success', 'Report scheduled successfully!');
        });

        $this->closeScheduleModal();
    }

    public function deleteTemplate($templateId)
    {
        $template = ReportTemplate::findOrFail($templateId);

        if (!$template->canBeEditedBy(Auth::user())) {
            session()->flash('error', 'You do not have permission to delete this template.');
            return;
        }

        $template->delete();
        session()->flash('success', 'Report template deleted successfully!');
    }

    public function toggleScheduleStatus($scheduleId)
    {
        $schedule = ScheduledReport::findOrFail($scheduleId);

        if ($schedule->created_by !== Auth::id()) {
            session()->flash('error', 'You do not have permission to modify this schedule.');
            return;
        }

        if ($schedule->status === 'active') {
            $schedule->pause();
            session()->flash('success', 'Report schedule paused.');
        } else {
            $schedule->resume();
            session()->flash('success', 'Report schedule resumed.');
        }
    }

    private function calculateReportSummary(): array
    {
        if (empty($this->generated_report)) {
            return [];
        }

        // Calculate basic summary statistics
        return [
            'total_records' => count($this->generated_report),
            'generated_at' => now()->format('Y-m-d H:i:s'),
            'date_range' => $this->date_from . ' to ' . $this->date_to,
        ];
    }

    private function calculateNextRunDate(): Carbon
    {
        $baseDate = now();

        return match($this->schedule_frequency) {
            'daily' => $baseDate->addDay(),
            'weekly' => $baseDate->addWeek(),
            'monthly' => $baseDate->addMonth()->day($this->schedule_day),
            'quarterly' => $baseDate->addMonths(3),
            'yearly' => $baseDate->addYear(),
            default => $baseDate->addDay(),
        };
    }

    private function generateExportFile(string $format): string
    {
        // This would implement actual file generation
        // For now, return a placeholder
        return storage_path('app/reports/report_' . time() . '.' . $format);
    }

    private function resetTemplateForm()
    {
        $this->template_name = '';
        $this->template_type = 'financial';
        $this->template_description = '';
        $this->template_configuration = [];
        $this->template_filters = [];
        $this->export_format = 'pdf';
        $this->is_public = false;
    }

    private function resetScheduleForm()
    {
        $this->schedule_name = '';
        $this->schedule_frequency = 'monthly';
        $this->schedule_time = '09:00';
        $this->schedule_day = 1;
        $this->recipients = '';
        $this->schedule_filters = [];
    }

    public function clearFilters()
    {
        $this->filter_type = '';
        $this->search = '';
        $this->resetPage();
    }
}
