<?php

namespace App\Livewire;

use App\Models\Branch;
use App\Models\Member;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use Livewire\Attributes\Validate;

class MemberManagement extends Component
{
    use WithPagination, WithFileUploads;

    // Search and filters
    public $search = '';
    public $branchFilter = '';
    public $statusFilter = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';

    // Modal states
    public $showViewModal = false;
    public $showEditModal = false;
    public $selectedMember = null;

    // Edit form fields
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|string|max:255')]
    public $father_or_husband_name = '';

    #[Validate('required|string|max:255')]
    public $mother_name = '';

    #[Validate('required|date|before:today')]
    public $date_of_birth = '';

    #[Validate('required|string|max:100')]
    public $religion = '';

    #[Validate('required|string|max:20')]
    public $phone_number = '';

    #[Validate('nullable|string|max:10')]
    public $blood_group = '';

    #[Validate('required|string')]
    public $present_address = '';

    #[Validate('required|string')]
    public $permanent_address = '';

    #[Validate('required|string|max:255')]
    public $occupation = '';

    #[Validate('nullable|image|max:2048')]
    public $newPhoto;

    #[Validate('nullable|exists:members,id')]
    public $reference_id = '';

    #[Validate('required|exists:branches,id')]
    public $branch_id = '';

    #[Validate('boolean')]
    public $is_active = true;

    // Bulk actions
    public $selectedMembers = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'branchFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount()
    {
        // Set default branch filter for field officers
        if (Auth::user()->isFieldOfficer() && Auth::user()->branch_id) {
            $this->branchFilter = Auth::user()->branch_id;
        }
        
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedBranchFilter()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedMembers = $this->getMembers()->pluck('id')->toArray();
        } else {
            $this->selectedMembers = [];
        }
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        
        $this->resetPage();
    }

    public function getMembers()
    {
        $query = Member::query()
            ->with(['branch', 'creator', 'reference'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('member_id', 'like', '%' . $this->search . '%')
                      ->orWhere('phone_number', 'like', '%' . $this->search . '%')
                      ->orWhere('nid_number', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->branchFilter, function ($query) {
                $query->where('branch_id', $this->branchFilter);
            })
            ->when($this->statusFilter !== '', function ($query) {
                $query->where('is_active', $this->statusFilter);
            });

        // Apply role-based filtering
        if (Auth::user()->isFieldOfficer() && Auth::user()->branch_id) {
            $query->where('branch_id', Auth::user()->branch_id);
        }

        return $query->orderBy($this->sortBy, $this->sortDirection)->paginate(10);
    }

    public function viewMember($memberId)
    {
        $this->selectedMember = Member::with([
            'branch', 
            'creator', 
            'reference', 
            'loanApplications.reviewer',
            'activeLoans.installments',
            'activeSavingAccounts'
        ])->findOrFail($memberId);
        
        $this->showViewModal = true;
    }

    public function editMember($memberId)
    {
        $member = Member::findOrFail($memberId);
        $this->selectedMember = $member;
        
        // Populate form fields
        $this->name = $member->name;
        $this->father_or_husband_name = $member->father_or_husband_name;
        $this->mother_name = $member->mother_name;
        $this->date_of_birth = $member->date_of_birth->format('Y-m-d');
        $this->religion = $member->religion;
        $this->phone_number = $member->phone_number;
        $this->blood_group = $member->blood_group;
        $this->present_address = $member->present_address;
        $this->permanent_address = $member->permanent_address;
        $this->occupation = $member->occupation;
        $this->reference_id = $member->reference_id;
        $this->branch_id = $member->branch_id;
        $this->is_active = $member->is_active;
        
        $this->showEditModal = true;
    }

    public function updateMember()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'father_or_husband_name' => $this->father_or_husband_name,
            'mother_name' => $this->mother_name,
            'date_of_birth' => $this->date_of_birth,
            'religion' => $this->religion,
            'phone_number' => $this->phone_number,
            'blood_group' => $this->blood_group,
            'present_address' => $this->present_address,
            'permanent_address' => $this->permanent_address,
            'occupation' => $this->occupation,
            'reference_id' => $this->reference_id ?: null,
            'branch_id' => $this->branch_id,
            'is_active' => $this->is_active,
        ];

        // Handle photo upload
        if ($this->newPhoto) {
            // Delete old photo if exists
            if ($this->selectedMember->photo && Storage::disk('public')->exists($this->selectedMember->photo)) {
                Storage::disk('public')->delete($this->selectedMember->photo);
            }

            // Store new photo
            $data['photo'] = $this->newPhoto->store('member-photos', 'public');
        }

        $this->selectedMember->update($data);

        $this->closeEditModal();
        session()->flash('message', 'Member updated successfully!');
    }

    public function toggleMemberStatus($memberId)
    {
        $member = Member::findOrFail($memberId);
        $member->update(['is_active' => !$member->is_active]);
        
        session()->flash('message', 'Member status updated successfully!');
    }

    public function deleteMember($memberId)
    {
        $member = Member::findOrFail($memberId);
        
        // Check if member has active loans or savings
        if ($member->activeLoans()->count() > 0 || $member->activeSavingAccounts()->count() > 0) {
            session()->flash('error', 'Cannot delete member with active loans or savings accounts.');
            return;
        }

        // Delete photo if exists
        if ($member->photo && Storage::disk('public')->exists($member->photo)) {
            Storage::disk('public')->delete($member->photo);
        }

        $member->delete();
        session()->flash('message', 'Member deleted successfully!');
    }

    public function bulkActivate()
    {
        Member::whereIn('id', $this->selectedMembers)->update(['is_active' => true]);
        
        $this->selectedMembers = [];
        $this->selectAll = false;
        session()->flash('message', 'Selected members activated successfully!');
    }

    public function bulkDeactivate()
    {
        Member::whereIn('id', $this->selectedMembers)->update(['is_active' => false]);
        
        $this->selectedMembers = [];
        $this->selectAll = false;
        session()->flash('message', 'Selected members deactivated successfully!');
    }

    public function exportMembers()
    {
        // This would typically generate an Excel/CSV file
        session()->flash('message', 'Export functionality will be implemented soon!');
    }

    public function closeViewModal()
    {
        $this->showViewModal = false;
        $this->selectedMember = null;
    }

    public function closeEditModal()
    {
        $this->showEditModal = false;
        $this->selectedMember = null;
        $this->newPhoto = null;
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->name = '';
        $this->father_or_husband_name = '';
        $this->mother_name = '';
        $this->date_of_birth = '';
        $this->religion = '';
        $this->phone_number = '';
        $this->blood_group = '';
        $this->present_address = '';
        $this->permanent_address = '';
        $this->occupation = '';
        $this->reference_id = '';
        $this->branch_id = '';
        $this->is_active = true;
        $this->resetValidation();
    }

    public function render()
    {
        return view('livewire.member-management', [
            'members' => $this->getMembers(),
            'branches' => Branch::all(),
            'allMembers' => Member::active()->get(),
            'bloodGroups' => ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
            'religions' => ['Islam', 'Hinduism', 'Christianity', 'Buddhism', 'Others'],
        ]);
    }
}
