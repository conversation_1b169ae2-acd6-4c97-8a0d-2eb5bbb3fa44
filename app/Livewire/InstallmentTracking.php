<?php

namespace App\Livewire;

use App\Models\Installment;
use App\Models\Member;
use App\Models\Loan;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class InstallmentTracking extends Component
{
    use WithPagination;

    // View mode
    public $view_mode = 'calendar'; // calendar, list, member
    
    // Filters
    public $selected_date;
    public $status_filter = '';
    public $branch_filter = '';
    public $search = '';
    
    // Calendar data
    public $calendar_month;
    public $calendar_year;
    public $calendar_data = [];
    
    // Member details
    public $selected_member = null;
    public $member_installments = [];
    public $show_member_modal = false;
    
    // Reminder settings
    public $show_reminder_modal = false;
    public $reminder_type = 'sms'; // sms, email, both
    public $reminder_message = '';
    public $selected_installments_for_reminder = [];

    protected $rules = [
        'reminder_message' => 'required|string|min:10|max:160',
    ];

    public function mount()
    {
        $this->selected_date = Carbon::now()->format('Y-m-d');
        $this->calendar_month = Carbon::now()->month;
        $this->calendar_year = Carbon::now()->year;
        $this->loadCalendarData();
    }

    public function updatedViewMode()
    {
        if ($this->view_mode === 'calendar') {
            $this->loadCalendarData();
        }
    }

    public function updatedCalendarMonth()
    {
        $this->loadCalendarData();
    }

    public function updatedCalendarYear()
    {
        $this->loadCalendarData();
    }

    public function loadCalendarData()
    {
        $startDate = Carbon::create($this->calendar_year, $this->calendar_month, 1)->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();
        
        $query = Installment::with(['loan.loanApplication.member'])
            ->whereBetween('installment_date', [$startDate, $endDate]);

        // Branch filter for field officers
        if (Auth::user()->role === 'field_officer') {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', Auth::user()->branch_id);
            });
        } elseif ($this->branch_filter) {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', $this->branch_filter);
            });
        }

        $installments = $query->get()->groupBy(function ($installment) {
            return $installment->installment_date->format('Y-m-d');
        });

        $this->calendar_data = [];
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dateString = $date->format('Y-m-d');
            $dayInstallments = $installments->get($dateString, collect());
            
            $this->calendar_data[$dateString] = [
                'date' => $date->copy(),
                'total_amount' => $dayInstallments->sum('installment_amount'),
                'pending_count' => $dayInstallments->where('status', 'pending')->count(),
                'paid_count' => $dayInstallments->where('status', 'paid')->count(),
                'overdue_count' => $dayInstallments->where('status', 'pending')
                    ->where('installment_date', '<', Carbon::now()->toDateString())->count(),
                'installments' => $dayInstallments,
            ];
        }
    }

    public function selectDate($date)
    {
        $this->selected_date = $date;
        $this->view_mode = 'list';
    }

    public function previousMonth()
    {
        $date = Carbon::create($this->calendar_year, $this->calendar_month, 1)->subMonth();
        $this->calendar_month = $date->month;
        $this->calendar_year = $date->year;
        $this->loadCalendarData();
    }

    public function nextMonth()
    {
        $date = Carbon::create($this->calendar_year, $this->calendar_month, 1)->addMonth();
        $this->calendar_month = $date->month;
        $this->calendar_year = $date->year;
        $this->loadCalendarData();
    }

    public function getInstallmentsQuery()
    {
        $query = Installment::with(['loan.loanApplication.member', 'collector'])
            ->when($this->selected_date, function ($q) {
                $q->whereDate('installment_date', $this->selected_date);
            })
            ->when($this->status_filter, function ($q) {
                if ($this->status_filter === 'overdue') {
                    $q->where('status', 'pending')
                      ->where('installment_date', '<', Carbon::now()->toDateString());
                } else {
                    $q->where('status', $this->status_filter);
                }
            })
            ->when($this->search, function ($q) {
                $q->whereHas('loan.loanApplication.member', function ($memberQuery) {
                    $memberQuery->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('member_id', 'like', '%' . $this->search . '%')
                        ->orWhere('phone_number', 'like', '%' . $this->search . '%');
                });
            });

        // Branch filter for field officers
        if (Auth::user()->role === 'field_officer') {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', Auth::user()->branch_id);
            });
        } elseif ($this->branch_filter) {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', $this->branch_filter);
            });
        }

        return $query->orderBy('installment_date', 'asc');
    }

    public function openMemberModal($memberId)
    {
        $this->selected_member = Member::with(['loanApplications.loan.installments'])
            ->find($memberId);
        
        $this->member_installments = Installment::with(['loan', 'collector'])
            ->whereHas('loan.loanApplication', function ($query) use ($memberId) {
                $query->where('member_id', $memberId);
            })
            ->orderBy('installment_date', 'desc')
            ->get();
            
        $this->show_member_modal = true;
    }

    public function closeMemberModal()
    {
        $this->show_member_modal = false;
        $this->selected_member = null;
        $this->member_installments = [];
    }

    public function openReminderModal($installmentIds = [])
    {
        $this->selected_installments_for_reminder = is_array($installmentIds) ? $installmentIds : [$installmentIds];
        $this->reminder_message = $this->getDefaultReminderMessage();
        $this->show_reminder_modal = true;
    }

    public function closeReminderModal()
    {
        $this->show_reminder_modal = false;
        $this->selected_installments_for_reminder = [];
        $this->reminder_message = '';
        $this->resetValidation();
    }

    private function getDefaultReminderMessage()
    {
        return "Dear member, your loan installment is due. Please make the payment at your earliest convenience. Thank you.";
    }

    public function sendReminders()
    {
        $this->validate();

        $installments = Installment::with(['loan.loanApplication.member'])
            ->whereIn('id', $this->selected_installments_for_reminder)
            ->get();

        $sentCount = 0;
        foreach ($installments as $installment) {
            $member = $installment->loan->loanApplication->member;
            
            // Here you would integrate with SMS/Email service
            // For now, we'll just simulate sending
            if ($this->reminder_type === 'sms' || $this->reminder_type === 'both') {
                // Send SMS
                // SmsService::send($member->phone_number, $this->reminder_message);
            }
            
            if ($this->reminder_type === 'email' || $this->reminder_type === 'both') {
                // Send Email
                // Mail::to($member->email)->send(new InstallmentReminder($installment, $this->reminder_message));
            }
            
            $sentCount++;
        }

        session()->flash('success', "Reminders sent to {$sentCount} members successfully!");
        $this->closeReminderModal();
    }

    public function getOverdueInstallments()
    {
        return Installment::with(['loan.loanApplication.member'])
            ->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now()->toDateString())
            ->whereHas('loan.loanApplication.member', function ($memberQuery) {
                if (Auth::user()->role === 'field_officer') {
                    $memberQuery->where('branch_id', Auth::user()->branch_id);
                }
            })
            ->get();
    }

    public function getDueTodayInstallments()
    {
        return Installment::with(['loan.loanApplication.member'])
            ->where('status', 'pending')
            ->whereDate('installment_date', Carbon::now()->toDateString())
            ->whereHas('loan.loanApplication.member', function ($memberQuery) {
                if (Auth::user()->role === 'field_officer') {
                    $memberQuery->where('branch_id', Auth::user()->branch_id);
                }
            })
            ->get();
    }

    public function sendBulkReminders($type)
    {
        $installments = $type === 'overdue' ? $this->getOverdueInstallments() : $this->getDueTodayInstallments();
        $installmentIds = $installments->pluck('id')->toArray();
        
        if (count($installmentIds) > 0) {
            $this->openReminderModal($installmentIds);
        } else {
            session()->flash('info', 'No installments found for bulk reminders.');
        }
    }

    public function render()
    {
        $data = [];
        
        if ($this->view_mode === 'list') {
            $data['installments'] = $this->getInstallmentsQuery()->paginate(15);
        }
        
        $data['overdue_installments'] = $this->getOverdueInstallments();
        $data['due_today_installments'] = $this->getDueTodayInstallments();
        
        return view('livewire.installment-tracking', $data);
    }
}
