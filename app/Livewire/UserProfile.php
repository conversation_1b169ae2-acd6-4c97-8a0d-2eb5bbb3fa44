<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Validate;

class UserProfile extends Component
{
    use WithFileUploads;

    // Profile form fields
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|email|max:255')]
    public $email = '';

    public $photo;

    #[Validate('nullable|image|max:2048')]
    public $newPhoto;

    // Password change fields
    #[Validate('required|string')]
    public $current_password = '';

    #[Validate('required|string|min:8|confirmed')]
    public $password = '';

    public $password_confirmation = '';

    // Modal states
    public $showPasswordModal = false;

    public function mount()
    {
        $user = Auth::user();
        $this->name = $user->name;
        $this->email = $user->email;
        $this->photo = $user->photo ?? null;
    }

    public function updateProfile()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . Auth::id(),
            'newPhoto' => 'nullable|image|max:2048',
        ]);

        $user = Auth::user();
        $data = [
            'name' => $this->name,
            'email' => $this->email,
        ];

        // Handle photo upload
        if ($this->newPhoto) {
            // Delete old photo if exists
            if ($user->photo && Storage::disk('public')->exists($user->photo)) {
                Storage::disk('public')->delete($user->photo);
            }

            // Store new photo
            $photoPath = $this->newPhoto->store('profile-photos', 'public');
            $data['photo'] = $photoPath;
            $this->photo = $photoPath;
        }

        $user->update($data);

        $this->newPhoto = null;
        session()->flash('profile-message', 'Profile updated successfully!');
    }

    public function deletePhoto()
    {
        $user = Auth::user();
        
        if ($user->photo && Storage::disk('public')->exists($user->photo)) {
            Storage::disk('public')->delete($user->photo);
        }

        $user->update(['photo' => null]);
        $this->photo = null;

        session()->flash('profile-message', 'Photo deleted successfully!');
    }

    public function openPasswordModal()
    {
        $this->resetPasswordForm();
        $this->showPasswordModal = true;
    }

    public function updatePassword()
    {
        $this->validate([
            'current_password' => 'required|string',
            'password' => ['required', 'string', 'confirmed', Password::defaults()],
        ]);

        $user = Auth::user();

        // Verify current password
        if (!Hash::check($this->current_password, $user->password)) {
            $this->addError('current_password', 'The current password is incorrect.');
            return;
        }

        $user->update([
            'password' => Hash::make($this->password),
        ]);

        $this->closePasswordModal();
        session()->flash('password-message', 'Password updated successfully!');
    }

    public function closePasswordModal()
    {
        $this->showPasswordModal = false;
        $this->resetPasswordForm();
    }

    private function resetPasswordForm()
    {
        $this->current_password = '';
        $this->password = '';
        $this->password_confirmation = '';
        $this->resetValidation(['current_password', 'password', 'password_confirmation']);
    }

    public function getPhotoUrlProperty()
    {
        if ($this->photo) {
            return Storage::disk('public')->url($this->photo);
        }
        
        return null;
    }

    public function render()
    {
        return view('livewire.user-profile', [
            'user' => Auth::user(),
        ]);
    }
}
