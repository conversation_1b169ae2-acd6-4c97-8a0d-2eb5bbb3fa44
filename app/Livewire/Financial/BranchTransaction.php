<?php

namespace App\Livewire\Financial;

use App\Models\BranchTransaction as Transaction;
use App\Models\TransactionCategory;
use App\Models\Branch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class BranchTransaction extends Component
{
    use WithPagination;

    // Form properties
    public $entry_type = 'income';
    public $description = '';
    public $account_no = '';
    public $category = '';
    public $voucher_no = '';
    public $amount = '';
    public $date = '';

    // Filter properties
    public $filter_type = '';
    public $filter_category = '';
    public $filter_date_from = '';
    public $filter_date_to = '';
    public $search = '';

    // Modal properties
    public $showModal = false;
    public $editingTransaction = null;

    // Approval properties
    public $showApprovalModal = false;
    public $pendingTransaction = null;
    public $approval_comments = '';

    protected $rules = [
        'entry_type' => 'required|in:income,expense',
        'description' => 'required|string|max:500',
        'account_no' => 'required|string|max:50',
        'category' => 'required|string|max:100',
        'amount' => 'required|numeric|min:0.01',
        'date' => 'required|date',
        'voucher_no' => 'nullable|string|max:50',
    ];

    public function mount()
    {
        $this->date = now()->format('Y-m-d');
        $this->filter_date_from = now()->startOfMonth()->format('Y-m-d');
        $this->filter_date_to = now()->format('Y-m-d');
    }

    public function render()
    {
        $transactions = $this->getTransactions();
        $categories = TransactionCategory::active()->get();
        $summary = $this->getTransactionSummary();

        return view('livewire.financial.branch-transaction', [
            'transactions' => $transactions,
            'categories' => $categories,
            'summary' => $summary,
        ]);
    }

    private function getTransactions()
    {
        $query = Transaction::with(['branch', 'enteredBy'])
            ->where('branch_id', Auth::user()->branch_id);

        // Apply filters
        if ($this->filter_type) {
            $query->where('entry_type', $this->filter_type);
        }

        if ($this->filter_category) {
            $query->where('category', $this->filter_category);
        }

        if ($this->filter_date_from) {
            $query->whereDate('date', '>=', $this->filter_date_from);
        }

        if ($this->filter_date_to) {
            $query->whereDate('date', '<=', $this->filter_date_to);
        }

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('description', 'like', '%' . $this->search . '%')
                  ->orWhere('account_no', 'like', '%' . $this->search . '%')
                  ->orWhere('voucher_no', 'like', '%' . $this->search . '%');
            });
        }

        return $query->orderBy('date', 'desc')
                    ->orderBy('created_at', 'desc')
                    ->paginate(15);
    }

    private function getTransactionSummary()
    {
        $query = Transaction::where('branch_id', Auth::user()->branch_id);

        if ($this->filter_date_from) {
            $query->whereDate('date', '>=', $this->filter_date_from);
        }

        if ($this->filter_date_to) {
            $query->whereDate('date', '<=', $this->filter_date_to);
        }

        $income = $query->clone()->where('entry_type', 'income')->sum('amount');
        $expense = $query->clone()->where('entry_type', 'expense')->sum('amount');

        return [
            'total_income' => $income,
            'total_expense' => $expense,
            'net_amount' => $income - $expense,
        ];
    }

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->editingTransaction = null;
        $this->resetForm();
    }

    public function editTransaction($transactionId)
    {
        $transaction = Transaction::findOrFail($transactionId);

        $this->editingTransaction = $transaction;
        $this->entry_type = $transaction->entry_type;
        $this->description = $transaction->description;
        $this->account_no = $transaction->account_no;
        $this->category = $transaction->category;
        $this->voucher_no = $transaction->voucher_no;
        $this->amount = $transaction->amount;
        $this->date = $transaction->date->format('Y-m-d');

        $this->showModal = true;
    }

    public function saveTransaction()
    {
        $this->validate();

        DB::transaction(function () {
            $data = [
                'branch_id' => Auth::user()->branch_id,
                'entry_type' => $this->entry_type,
                'description' => $this->description,
                'account_no' => $this->account_no,
                'category' => $this->category,
                'voucher_no' => $this->voucher_no,
                'amount' => $this->amount,
                'date' => $this->date,
                'entered_by' => Auth::id(),
            ];

            if ($this->editingTransaction) {
                $this->editingTransaction->update($data);
                session()->flash('success', 'Transaction updated successfully!');
            } else {
                // Generate serial number
                $data['serial_no'] = $this->generateSerialNumber();
                Transaction::create($data);
                session()->flash('success', 'Transaction created successfully!');
            }
        });

        $this->closeModal();
    }

    private function generateSerialNumber()
    {
        $lastTransaction = Transaction::where('branch_id', Auth::user()->branch_id)
            ->whereYear('date', Carbon::parse($this->date)->year)
            ->orderBy('serial_no', 'desc')
            ->first();

        return $lastTransaction ? $lastTransaction->serial_no + 1 : 1;
    }

    public function deleteTransaction($transactionId)
    {
        $transaction = Transaction::findOrFail($transactionId);

        // Check if user has permission to delete
        if ($transaction->entered_by !== Auth::id() && !Auth::user()->hasRole(['admin', 'manager'])) {
            session()->flash('error', 'You do not have permission to delete this transaction.');
            return;
        }

        $transaction->delete();
        session()->flash('success', 'Transaction deleted successfully!');
    }

    private function resetForm()
    {
        $this->entry_type = 'income';
        $this->description = '';
        $this->account_no = '';
        $this->category = '';
        $this->voucher_no = '';
        $this->amount = '';
        $this->date = now()->format('Y-m-d');
    }

    public function clearFilters()
    {
        $this->filter_type = '';
        $this->filter_category = '';
        $this->filter_date_from = now()->startOfMonth()->format('Y-m-d');
        $this->filter_date_to = now()->format('Y-m-d');
        $this->search = '';
        $this->resetPage();
    }

    public function exportTransactions()
    {
        // This would implement CSV/Excel export functionality
        session()->flash('info', 'Export functionality will be implemented soon.');
    }
}
