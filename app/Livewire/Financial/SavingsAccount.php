<?php

namespace App\Livewire\Financial;

use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use App\Models\Member;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Carbon\Carbon;

class SavingsAccount extends Component
{
    use WithFileUploads, WithPagination;

    // Form properties for account creation
    public $member_id = '';
    public $saving_type = 'general';
    public $joint_photo = null;
    public $nominee_name = '';
    public $nominee_relation = '';
    public $saving_method = 'daily';
    public $monthly_amount = '';
    public $fdr_amount = '';
    public $start_date = '';

    // Transaction properties
    public $selected_account_id = '';
    public $transaction_type = 'deposit';
    public $transaction_amount = '';
    public $transaction_description = '';

    // Modal properties
    public $showAccountModal = false;
    public $showTransactionModal = false;
    public $editingAccount = null;

    // Filter properties
    public $filter_type = '';
    public $filter_status = '';
    public $search = '';

    protected $rules = [
        'member_id' => 'required|exists:members,id',
        'saving_type' => 'required|in:general,dps,fdr',
        'nominee_name' => 'required|string|max:100',
        'nominee_relation' => 'required|string|max:50',
        'saving_method' => 'required|in:daily,weekly',
        'monthly_amount' => 'required|numeric|min:100',
        'fdr_amount' => 'nullable|numeric|min:1000',
        'start_date' => 'required|date',
        'joint_photo' => 'nullable|image|max:2048',
    ];

    protected $messages = [
        'member_id.required' => 'Please select a member.',
        'monthly_amount.min' => 'Minimum monthly amount is ৳100.',
        'fdr_amount.min' => 'Minimum FDR amount is ৳1,000.',
    ];

    public function mount()
    {
        $this->start_date = now()->format('Y-m-d');
    }

    public function render()
    {
        $accounts = $this->getSavingAccounts();
        $members = Member::where('branch_id', Auth::user()->branch_id)
                        ->where('is_active', true)
                        ->orderBy('name')
                        ->get();

        return view('livewire.financial.savings-account', [
            'accounts' => $accounts,
            'members' => $members,
        ]);
    }

    private function getSavingAccounts()
    {
        $query = SavingAccount::with(['member', 'creator'])
            ->whereHas('member', function($q) {
                $q->where('branch_id', Auth::user()->branch_id);
            });

        // Apply filters
        if ($this->filter_type) {
            $query->where('saving_type', $this->filter_type);
        }

        if ($this->filter_status === 'active') {
            $query->where('is_active', true);
        } elseif ($this->filter_status === 'inactive') {
            $query->where('is_active', false);
        }

        if ($this->search) {
            $query->whereHas('member', function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('member_id', 'like', '%' . $this->search . '%');
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate(15);
    }

    public function openAccountModal()
    {
        $this->resetAccountForm();
        $this->showAccountModal = true;
    }

    public function closeAccountModal()
    {
        $this->showAccountModal = false;
        $this->editingAccount = null;
        $this->resetAccountForm();
    }

    public function editAccount($accountId)
    {
        $account = SavingAccount::findOrFail($accountId);

        $this->editingAccount = $account;
        $this->member_id = $account->member_id;
        $this->saving_type = $account->saving_type;
        $this->nominee_name = $account->nominee_name;
        $this->nominee_relation = $account->nominee_relation;
        $this->saving_method = $account->saving_method;
        $this->monthly_amount = $account->monthly_amount;
        $this->fdr_amount = $account->fdr_amount;
        $this->start_date = $account->start_date->format('Y-m-d');

        $this->showAccountModal = true;
    }

    public function saveAccount()
    {
        $this->validate();

        DB::transaction(function () {
            $data = [
                'member_id' => $this->member_id,
                'saving_type' => $this->saving_type,
                'nominee_name' => $this->nominee_name,
                'nominee_relation' => $this->nominee_relation,
                'saving_method' => $this->saving_method,
                'monthly_amount' => $this->monthly_amount,
                'fdr_amount' => $this->saving_type === 'fdr' ? $this->fdr_amount : null,
                'start_date' => $this->start_date,
                'created_by' => Auth::id(),
                'is_active' => true,
            ];

            // Handle joint photo upload
            if ($this->joint_photo) {
                $data['joint_photo'] = $this->joint_photo->store('savings/joint-photos', 'public');
            }

            if ($this->editingAccount) {
                $this->editingAccount->update($data);
                session()->flash('success', 'Saving account updated successfully!');
            } else {
                $account = SavingAccount::create($data);

                // Create initial deposit for FDR
                if ($this->saving_type === 'fdr' && $this->fdr_amount > 0) {
                    $account->deposit($this->fdr_amount, Auth::user(), 'Initial FDR deposit');
                }

                session()->flash('success', 'Saving account created successfully!');
            }
        });

        $this->closeAccountModal();
    }

    public function openTransactionModal($accountId)
    {
        $this->selected_account_id = $accountId;
        $this->resetTransactionForm();
        $this->showTransactionModal = true;
    }

    public function closeTransactionModal()
    {
        $this->showTransactionModal = false;
        $this->resetTransactionForm();
    }

    public function processTransaction()
    {
        $this->validate([
            'transaction_amount' => 'required|numeric|min:1',
            'transaction_description' => 'nullable|string|max:255',
        ]);

        $account = SavingAccount::findOrFail($this->selected_account_id);

        try {
            DB::transaction(function () use ($account) {
                if ($this->transaction_type === 'deposit') {
                    $account->deposit(
                        $this->transaction_amount,
                        Auth::user(),
                        $this->transaction_description ?: 'Deposit'
                    );
                } else {
                    $account->withdraw(
                        $this->transaction_amount,
                        Auth::user(),
                        $this->transaction_description ?: 'Withdrawal'
                    );
                }
            });

            session()->flash('success', ucfirst($this->transaction_type) . ' processed successfully!');
            $this->closeTransactionModal();
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        }
    }

    public function toggleAccountStatus($accountId)
    {
        $account = SavingAccount::findOrFail($accountId);
        $account->update(['is_active' => !$account->is_active]);

        $status = $account->is_active ? 'activated' : 'deactivated';
        session()->flash('success', "Account {$status} successfully!");
    }

    public function viewAccountDetails($accountId)
    {
        // This would redirect to a detailed account view
        return redirect()->route('manager.savings-management', ['account' => $accountId]);
    }

    private function resetAccountForm()
    {
        $this->member_id = '';
        $this->saving_type = 'general';
        $this->joint_photo = null;
        $this->nominee_name = '';
        $this->nominee_relation = '';
        $this->saving_method = 'daily';
        $this->monthly_amount = '';
        $this->fdr_amount = '';
        $this->start_date = now()->format('Y-m-d');
    }

    private function resetTransactionForm()
    {
        $this->transaction_type = 'deposit';
        $this->transaction_amount = '';
        $this->transaction_description = '';
    }

    public function clearFilters()
    {
        $this->filter_type = '';
        $this->filter_status = '';
        $this->search = '';
        $this->resetPage();
    }

    public function updatedSavingType()
    {
        // Reset FDR amount when changing type
        if ($this->saving_type !== 'fdr') {
            $this->fdr_amount = '';
        }
    }

    public function calculateMaturityAmount()
    {
        if ($this->saving_type === 'fdr' && $this->fdr_amount > 0) {
            // Simple calculation: 10% annual interest for 1 year
            $interestRate = 10; // 10% annual
            $maturityAmount = $this->fdr_amount * (1 + ($interestRate / 100));

            session()->flash('info', "Estimated maturity amount after 1 year: ৳" . number_format($maturityAmount, 2));
        }
    }
}
