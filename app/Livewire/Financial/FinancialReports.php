<?php

namespace App\Livewire\Financial;

use App\Models\BranchTransaction;
use App\Models\TransactionCategory;
use App\Models\Budget;
use App\Models\Branch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Carbon\Carbon;

class FinancialReports extends Component
{
    public $report_type = 'cash_flow';
    public $date_from = '';
    public $date_to = '';
    public $branch_id = '';
    public $category_filter = '';

    public $report_data = [];
    public $chart_data = [];
    public $summary_stats = [];

    public function mount()
    {
        $this->date_from = now()->startOfMonth()->format('Y-m-d');
        $this->date_to = now()->format('Y-m-d');
        $this->branch_id = Auth::user()->branch_id;
    }

    public function render()
    {
        $branches = Branch::all();
        $categories = TransactionCategory::active()->get();

        return view('livewire.financial.financial-reports', [
            'branches' => $branches,
            'categories' => $categories,
        ]);
    }

    public function generateReport()
    {
        switch ($this->report_type) {
            case 'cash_flow':
                $this->generateCashFlowReport();
                break;
            case 'income_expense':
                $this->generateIncomeExpenseReport();
                break;
            case 'profit_loss':
                $this->generateProfitLossReport();
                break;
            case 'budget_analysis':
                $this->generateBudgetAnalysisReport();
                break;
            case 'category_breakdown':
                $this->generateCategoryBreakdownReport();
                break;
        }
    }

    private function generateCashFlowReport()
    {
        $query = BranchTransaction::where('branch_id', $this->branch_id)
            ->whereBetween('date', [$this->date_from, $this->date_to]);

        if ($this->category_filter) {
            $query->where('category', $this->category_filter);
        }

        $transactions = $query->orderBy('date')->get();

        $dailyData = [];
        $runningBalance = 0;

        foreach ($transactions->groupBy(function($item) {
            return $item->date->format('Y-m-d');
        }) as $date => $dayTransactions) {
            $dailyIncome = $dayTransactions->where('entry_type', 'income')->sum('amount');
            $dailyExpense = $dayTransactions->where('entry_type', 'expense')->sum('amount');
            $netFlow = $dailyIncome - $dailyExpense;
            $runningBalance += $netFlow;

            $dailyData[] = [
                'date' => $date,
                'income' => $dailyIncome,
                'expense' => $dailyExpense,
                'net_flow' => $netFlow,
                'running_balance' => $runningBalance,
            ];
        }

        $this->report_data = $dailyData;

        $this->summary_stats = [
            'total_income' => $transactions->where('entry_type', 'income')->sum('amount'),
            'total_expense' => $transactions->where('entry_type', 'expense')->sum('amount'),
            'net_cash_flow' => $transactions->where('entry_type', 'income')->sum('amount') -
                              $transactions->where('entry_type', 'expense')->sum('amount'),
            'final_balance' => $runningBalance,
        ];

        // Prepare chart data
        $this->chart_data = [
            'labels' => collect($dailyData)->pluck('date')->toArray(),
            'income' => collect($dailyData)->pluck('income')->toArray(),
            'expense' => collect($dailyData)->pluck('expense')->toArray(),
            'balance' => collect($dailyData)->pluck('running_balance')->toArray(),
        ];
    }

    private function generateIncomeExpenseReport()
    {
        $query = BranchTransaction::where('branch_id', $this->branch_id)
            ->whereBetween('date', [$this->date_from, $this->date_to]);

        if ($this->category_filter) {
            $query->where('category', $this->category_filter);
        }

        $transactions = $query->get();

        $monthlyData = [];

        foreach ($transactions->groupBy(function($item) {
            return $item->date->format('Y-m');
        }) as $month => $monthTransactions) {
            $income = $monthTransactions->where('entry_type', 'income')->sum('amount');
            $expense = $monthTransactions->where('entry_type', 'expense')->sum('amount');

            $monthlyData[] = [
                'month' => Carbon::createFromFormat('Y-m', $month)->format('M Y'),
                'income' => $income,
                'expense' => $expense,
                'net' => $income - $expense,
                'income_count' => $monthTransactions->where('entry_type', 'income')->count(),
                'expense_count' => $monthTransactions->where('entry_type', 'expense')->count(),
            ];
        }

        $this->report_data = $monthlyData;

        $totalIncome = $transactions->where('entry_type', 'income')->sum('amount');
        $totalExpense = $transactions->where('entry_type', 'expense')->sum('amount');

        $this->summary_stats = [
            'total_income' => $totalIncome,
            'total_expense' => $totalExpense,
            'net_income' => $totalIncome - $totalExpense,
            'avg_monthly_income' => count($monthlyData) > 0 ? $totalIncome / count($monthlyData) : 0,
            'avg_monthly_expense' => count($monthlyData) > 0 ? $totalExpense / count($monthlyData) : 0,
        ];

        // Prepare chart data
        $this->chart_data = [
            'labels' => collect($monthlyData)->pluck('month')->toArray(),
            'income' => collect($monthlyData)->pluck('income')->toArray(),
            'expense' => collect($monthlyData)->pluck('expense')->toArray(),
        ];
    }

    private function generateProfitLossReport()
    {
        $query = BranchTransaction::where('branch_id', $this->branch_id)
            ->whereBetween('date', [$this->date_from, $this->date_to]);

        $transactions = $query->get();

        // Group by category
        $incomeByCategory = $transactions->where('entry_type', 'income')
            ->groupBy('category')
            ->map(function($items) {
                return [
                    'amount' => $items->sum('amount'),
                    'count' => $items->count(),
                ];
            });

        $expenseByCategory = $transactions->where('entry_type', 'expense')
            ->groupBy('category')
            ->map(function($items) {
                return [
                    'amount' => $items->sum('amount'),
                    'count' => $items->count(),
                ];
            });

        $totalIncome = $incomeByCategory->sum('amount');
        $totalExpense = $expenseByCategory->sum('amount');
        $grossProfit = $totalIncome - $totalExpense;

        $this->report_data = [
            'income_categories' => $incomeByCategory,
            'expense_categories' => $expenseByCategory,
        ];

        $this->summary_stats = [
            'total_revenue' => $totalIncome,
            'total_expenses' => $totalExpense,
            'gross_profit' => $grossProfit,
            'profit_margin' => $totalIncome > 0 ? ($grossProfit / $totalIncome) * 100 : 0,
        ];
    }

    private function generateBudgetAnalysisReport()
    {
        $budgets = Budget::with(['category', 'branch'])
            ->where('branch_id', $this->branch_id)
            ->where('start_date', '<=', $this->date_to)
            ->where('end_date', '>=', $this->date_from)
            ->get();

        $budgetData = [];

        foreach ($budgets as $budget) {
            $actualSpent = BranchTransaction::where('branch_id', $this->branch_id)
                ->where('category', $budget->category->name)
                ->where('entry_type', 'expense')
                ->whereBetween('date', [
                    max($budget->start_date, $this->date_from),
                    min($budget->end_date, $this->date_to)
                ])
                ->sum('amount');

            $variance = $budget->allocated_amount - $actualSpent;
            $utilizationPercent = $budget->allocated_amount > 0 ?
                ($actualSpent / $budget->allocated_amount) * 100 : 0;

            $budgetData[] = [
                'category' => $budget->category->name,
                'allocated' => $budget->allocated_amount,
                'spent' => $actualSpent,
                'remaining' => $variance,
                'utilization_percent' => $utilizationPercent,
                'status' => $utilizationPercent > 100 ? 'Over Budget' :
                           ($utilizationPercent > 80 ? 'Near Limit' : 'Within Budget'),
            ];
        }

        $this->report_data = $budgetData;

        $this->summary_stats = [
            'total_allocated' => collect($budgetData)->sum('allocated'),
            'total_spent' => collect($budgetData)->sum('spent'),
            'total_remaining' => collect($budgetData)->sum('remaining'),
            'overall_utilization' => collect($budgetData)->avg('utilization_percent'),
            'over_budget_count' => collect($budgetData)->where('utilization_percent', '>', 100)->count(),
        ];
    }

    private function generateCategoryBreakdownReport()
    {
        $query = BranchTransaction::where('branch_id', $this->branch_id)
            ->whereBetween('date', [$this->date_from, $this->date_to]);

        $transactions = $query->get();

        $categoryData = $transactions->groupBy(['entry_type', 'category'])
            ->map(function($typeGroup, $type) {
                return $typeGroup->map(function($categoryGroup, $category) {
                    return [
                        'amount' => $categoryGroup->sum('amount'),
                        'count' => $categoryGroup->count(),
                        'avg_amount' => $categoryGroup->avg('amount'),
                        'percentage' => 0, // Will be calculated below
                    ];
                });
            });

        // Calculate percentages
        foreach (['income', 'expense'] as $type) {
            if (isset($categoryData[$type])) {
                $totalAmount = $categoryData[$type]->sum('amount');
                $categoryData[$type] = $categoryData[$type]->map(function($item) use ($totalAmount) {
                    $item['percentage'] = $totalAmount > 0 ? ($item['amount'] / $totalAmount) * 100 : 0;
                    return $item;
                });
            }
        }

        $this->report_data = $categoryData;

        $this->summary_stats = [
            'total_categories' => $transactions->pluck('category')->unique()->count(),
            'most_used_income_category' => $categoryData['income'] ?? collect()->sortByDesc('count')->keys()->first(),
            'most_used_expense_category' => $categoryData['expense'] ?? collect()->sortByDesc('count')->keys()->first(),
            'highest_income_category' => $categoryData['income'] ?? collect()->sortByDesc('amount')->keys()->first(),
            'highest_expense_category' => $categoryData['expense'] ?? collect()->sortByDesc('amount')->keys()->first(),
        ];
    }

    public function exportReport()
    {
        // This would implement PDF/Excel export functionality
        session()->flash('info', 'Export functionality will be implemented soon.');
    }

    public function clearFilters()
    {
        $this->date_from = now()->startOfMonth()->format('Y-m-d');
        $this->date_to = now()->format('Y-m-d');
        $this->category_filter = '';
        $this->report_data = [];
        $this->chart_data = [];
        $this->summary_stats = [];
    }
}
