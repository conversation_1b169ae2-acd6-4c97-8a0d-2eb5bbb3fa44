<?php

namespace App\Livewire;

use App\Models\Installment;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Carbon\Carbon;

class InstallmentReports extends Component
{
    // Report filters
    public $report_type = 'daily_collection';
    public $date_from;
    public $date_to;
    public $branch_filter = '';
    public $officer_filter = '';
    public $status_filter = '';
    
    // Report data
    public $report_data = [];
    public $summary_stats = [];
    
    // Export settings
    public $export_format = 'excel';

    public function mount()
    {
        $this->date_from = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->date_to = Carbon::now()->format('Y-m-d');
        $this->generateReport();
    }

    public function updatedReportType()
    {
        $this->generateReport();
    }

    public function updatedDateFrom()
    {
        $this->generateReport();
    }

    public function updatedDateTo()
    {
        $this->generateReport();
    }

    public function updatedBranchFilter()
    {
        $this->generateReport();
    }

    public function updatedOfficerFilter()
    {
        $this->generateReport();
    }

    public function updatedStatusFilter()
    {
        $this->generateReport();
    }

    public function generateReport()
    {
        switch ($this->report_type) {
            case 'daily_collection':
                $this->generateDailyCollectionReport();
                break;
            case 'overdue_analysis':
                $this->generateOverdueAnalysisReport();
                break;
            case 'collection_efficiency':
                $this->generateCollectionEfficiencyReport();
                break;
            case 'officer_performance':
                $this->generateOfficerPerformanceReport();
                break;
            case 'branch_summary':
                $this->generateBranchSummaryReport();
                break;
        }
    }

    private function generateDailyCollectionReport()
    {
        $query = Installment::with(['loan.loanApplication.member', 'collector'])
            ->whereBetween('collection_date', [$this->date_from, $this->date_to])
            ->where('status', 'paid');

        $this->applyFilters($query);

        $collections = $query->get();
        
        $this->report_data = $collections->groupBy(function ($item) {
            return $item->collection_date->format('Y-m-d');
        })->map(function ($dayCollections, $date) {
            return [
                'date' => $date,
                'total_amount' => $dayCollections->sum('installment_amount'),
                'total_count' => $dayCollections->count(),
                'unique_members' => $dayCollections->unique('loan.loanApplication.member_id')->count(),
                'collections' => $dayCollections,
            ];
        })->sortBy('date');

        $this->summary_stats = [
            'total_amount' => $collections->sum('installment_amount'),
            'total_collections' => $collections->count(),
            'unique_members' => $collections->unique('loan.loanApplication.member_id')->count(),
            'average_daily' => $this->report_data->avg('total_amount'),
        ];
    }

    private function generateOverdueAnalysisReport()
    {
        $query = Installment::with(['loan.loanApplication.member'])
            ->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now()->toDateString());

        $this->applyFilters($query, false); // Don't filter by collection date

        $overdueInstallments = $query->get();
        
        $this->report_data = $overdueInstallments->groupBy(function ($item) {
            $daysPastDue = Carbon::now()->diffInDays($item->installment_date);
            if ($daysPastDue <= 7) return '1-7 days';
            if ($daysPastDue <= 30) return '8-30 days';
            if ($daysPastDue <= 90) return '31-90 days';
            return '90+ days';
        })->map(function ($group, $range) {
            return [
                'range' => $range,
                'count' => $group->count(),
                'total_amount' => $group->sum('installment_amount'),
                'installments' => $group,
            ];
        });

        $this->summary_stats = [
            'total_overdue_amount' => $overdueInstallments->sum('installment_amount'),
            'total_overdue_count' => $overdueInstallments->count(),
            'unique_members' => $overdueInstallments->unique('loan.loanApplication.member_id')->count(),
            'average_overdue_amount' => $overdueInstallments->avg('installment_amount'),
        ];
    }

    private function generateCollectionEfficiencyReport()
    {
        $startDate = Carbon::parse($this->date_from);
        $endDate = Carbon::parse($this->date_to);
        
        $this->report_data = [];
        
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dateString = $date->format('Y-m-d');
            
            // Get installments due on this date
            $dueQuery = Installment::with(['loan.loanApplication.member'])
                ->whereDate('installment_date', $dateString);
            
            $this->applyFilters($dueQuery, false);
            $dueInstallments = $dueQuery->get();
            
            // Get installments collected on this date
            $collectedQuery = Installment::with(['loan.loanApplication.member'])
                ->whereDate('collection_date', $dateString)
                ->where('status', 'paid');
            
            $this->applyFilters($collectedQuery);
            $collectedInstallments = $collectedQuery->get();
            
            $dueAmount = $dueInstallments->sum('installment_amount');
            $collectedAmount = $collectedInstallments->sum('installment_amount');
            $efficiency = $dueAmount > 0 ? ($collectedAmount / $dueAmount) * 100 : 0;
            
            $this->report_data[] = [
                'date' => $dateString,
                'due_count' => $dueInstallments->count(),
                'due_amount' => $dueAmount,
                'collected_count' => $collectedInstallments->count(),
                'collected_amount' => $collectedAmount,
                'efficiency_percentage' => $efficiency,
            ];
        }

        $totalDue = collect($this->report_data)->sum('due_amount');
        $totalCollected = collect($this->report_data)->sum('collected_amount');
        
        $this->summary_stats = [
            'overall_efficiency' => $totalDue > 0 ? ($totalCollected / $totalDue) * 100 : 0,
            'total_due_amount' => $totalDue,
            'total_collected_amount' => $totalCollected,
            'average_daily_efficiency' => collect($this->report_data)->avg('efficiency_percentage'),
        ];
    }

    private function generateOfficerPerformanceReport()
    {
        $query = Installment::with(['loan.loanApplication.member', 'collector'])
            ->whereBetween('collection_date', [$this->date_from, $this->date_to])
            ->where('status', 'paid')
            ->whereNotNull('collected_by');

        $this->applyFilters($query);

        $collections = $query->get();
        
        $this->report_data = $collections->groupBy('collected_by')->map(function ($officerCollections, $officerId) {
            $officer = User::find($officerId);
            return [
                'officer_id' => $officerId,
                'officer_name' => $officer->name ?? 'Unknown',
                'total_amount' => $officerCollections->sum('installment_amount'),
                'total_collections' => $officerCollections->count(),
                'unique_members' => $officerCollections->unique('loan.loanApplication.member_id')->count(),
                'average_per_collection' => $officerCollections->avg('installment_amount'),
                'collections' => $officerCollections,
            ];
        })->sortByDesc('total_amount');

        $this->summary_stats = [
            'total_officers' => $this->report_data->count(),
            'total_amount' => $collections->sum('installment_amount'),
            'total_collections' => $collections->count(),
            'top_performer' => $this->report_data->first()['officer_name'] ?? 'N/A',
        ];
    }

    private function generateBranchSummaryReport()
    {
        $query = Installment::with(['loan.loanApplication.member.branch', 'collector'])
            ->whereBetween('collection_date', [$this->date_from, $this->date_to])
            ->where('status', 'paid');

        if ($this->branch_filter) {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', $this->branch_filter);
            });
        }

        $collections = $query->get();
        
        $this->report_data = $collections->groupBy('loan.loanApplication.member.branch_id')->map(function ($branchCollections, $branchId) {
            $branch = Branch::find($branchId);
            return [
                'branch_id' => $branchId,
                'branch_name' => $branch->name ?? 'Unknown',
                'total_amount' => $branchCollections->sum('installment_amount'),
                'total_collections' => $branchCollections->count(),
                'unique_members' => $branchCollections->unique('loan.loanApplication.member_id')->count(),
                'unique_officers' => $branchCollections->unique('collected_by')->count(),
                'collections' => $branchCollections,
            ];
        })->sortByDesc('total_amount');

        $this->summary_stats = [
            'total_branches' => $this->report_data->count(),
            'total_amount' => $collections->sum('installment_amount'),
            'total_collections' => $collections->count(),
            'top_branch' => $this->report_data->first()['branch_name'] ?? 'N/A',
        ];
    }

    private function applyFilters($query, $useCollectionDate = true)
    {
        // Branch filter
        if ($this->branch_filter) {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', $this->branch_filter);
            });
        } elseif (Auth::user()->role === 'field_officer') {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', Auth::user()->branch_id);
            });
        }

        // Officer filter
        if ($this->officer_filter && $useCollectionDate) {
            $query->where('collected_by', $this->officer_filter);
        }

        // Status filter (for non-collection reports)
        if ($this->status_filter && !$useCollectionDate) {
            $query->where('status', $this->status_filter);
        }
    }

    public function exportReport()
    {
        // Here you would implement export functionality
        // For now, we'll just show a success message
        session()->flash('success', 'Report export functionality will be implemented with Excel/PDF libraries.');
    }

    public function getBranches()
    {
        if (Auth::user()->role === 'admin') {
            return Branch::all();
        } elseif (Auth::user()->role === 'manager') {
            return Branch::where('id', Auth::user()->branch_id)->get();
        }
        return collect();
    }

    public function getOfficers()
    {
        $query = User::where('role', 'field_officer');
        
        if ($this->branch_filter) {
            $query->where('branch_id', $this->branch_filter);
        } elseif (Auth::user()->role !== 'admin') {
            $query->where('branch_id', Auth::user()->branch_id);
        }
        
        return $query->get();
    }

    public function render()
    {
        return view('livewire.installment-reports', [
            'branches' => $this->getBranches(),
            'officers' => $this->getOfficers(),
        ]);
    }
}
