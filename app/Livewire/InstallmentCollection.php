<?php

namespace App\Livewire;

use App\Models\Installment;
use App\Models\Loan;
use App\Models\Member;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class InstallmentCollection extends Component
{
    use WithPagination;

    // Search and filters
    public $search = '';
    public $collection_date;
    public $status_filter = 'pending';
    public $amount_filter = '';
    
    // Member selection
    public $selected_member = null;
    public $member_loans = [];
    public $members = [];
    public $member_search = '';
    
    // Collection form
    public $show_collection_modal = false;
    public $collecting_installment = null;
    public $collection_amount = '';
    public $partial_payment = false;
    public $collection_notes = '';
    
    // Bulk collection
    public $selected_installments = [];
    public $select_all = false;
    
    // Quick stats
    public $today_collections = 0;
    public $today_target = 0;
    public $overdue_count = 0;

    protected $rules = [
        'collection_amount' => 'required|numeric|min:1',
        'collection_notes' => 'nullable|string|max:255',
    ];

    public function mount()
    {
        $this->collection_date = Carbon::now()->format('Y-m-d');
        $this->loadQuickStats();
        $this->loadMembers();
    }

    public function loadQuickStats()
    {
        $today = Carbon::now()->toDateString();
        $userId = Auth::id();
        
        // Today's collections by this officer
        $this->today_collections = Installment::where('collected_by', $userId)
            ->whereDate('collection_date', $today)
            ->sum('installment_amount');
            
        // Today's target (pending installments due today)
        $this->today_target = Installment::whereDate('installment_date', $today)
            ->where('status', 'pending')
            ->whereHas('loan.loanApplication.member', function ($query) {
                if (Auth::user()->role === 'field_officer') {
                    $query->where('branch_id', Auth::user()->branch_id);
                }
            })
            ->sum('installment_amount');
            
        // Overdue count
        $this->overdue_count = Installment::where('status', 'pending')
            ->where('installment_date', '<', $today)
            ->whereHas('loan.loanApplication.member', function ($query) {
                if (Auth::user()->role === 'field_officer') {
                    $query->where('branch_id', Auth::user()->branch_id);
                }
            })
            ->count();
    }

    public function loadMembers()
    {
        $query = Member::where('is_active', true)
            ->whereHas('loanApplications.loan.installments', function ($installmentQuery) {
                $installmentQuery->where('status', 'pending');
            });
        
        // If user is field officer, only show members from their branch
        if (Auth::user()->role === 'field_officer') {
            $query->where('branch_id', Auth::user()->branch_id);
        }
        
        if ($this->member_search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->member_search . '%')
                  ->orWhere('member_id', 'like', '%' . $this->member_search . '%')
                  ->orWhere('phone_number', 'like', '%' . $this->member_search . '%');
            });
        }
        
        $this->members = $query->limit(20)->get();
    }

    public function updatedMemberSearch()
    {
        $this->loadMembers();
    }

    public function selectMember($memberId)
    {
        $this->selected_member = Member::find($memberId);
        $this->member_search = '';
        $this->members = [];
        $this->loadMemberLoans();
    }

    public function clearMemberSelection()
    {
        $this->selected_member = null;
        $this->member_loans = [];
    }

    public function loadMemberLoans()
    {
        if ($this->selected_member) {
            $this->member_loans = Loan::with(['installments' => function ($query) {
                $query->where('status', 'pending')->orderBy('installment_date');
            }])
            ->whereHas('loanApplication', function ($query) {
                $query->where('member_id', $this->selected_member->id);
            })
            ->whereHas('installments', function ($query) {
                $query->where('status', 'pending');
            })
            ->get();
        }
    }

    public function getInstallmentsQuery()
    {
        $query = Installment::with(['loan.loanApplication.member', 'collector'])
            ->when($this->status_filter, function ($q) {
                if ($this->status_filter === 'due_today') {
                    $q->where('status', 'pending')
                      ->whereDate('installment_date', Carbon::now()->toDateString());
                } elseif ($this->status_filter === 'overdue') {
                    $q->where('status', 'pending')
                      ->where('installment_date', '<', Carbon::now()->toDateString());
                } else {
                    $q->where('status', $this->status_filter);
                }
            })
            ->when($this->search, function ($q) {
                $q->whereHas('loan.loanApplication.member', function ($memberQuery) {
                    $memberQuery->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('member_id', 'like', '%' . $this->search . '%')
                        ->orWhere('phone_number', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->collection_date, function ($q) {
                if ($this->status_filter === 'paid') {
                    $q->whereDate('collection_date', $this->collection_date);
                } else {
                    $q->whereDate('installment_date', $this->collection_date);
                }
            });

        // Branch filter for field officers
        if (Auth::user()->role === 'field_officer') {
            $query->whereHas('loan.loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', Auth::user()->branch_id);
            });
        }

        return $query->orderBy('installment_date', 'asc');
    }

    public function openCollectionModal($installmentId)
    {
        $this->collecting_installment = Installment::with(['loan.loanApplication.member'])
            ->find($installmentId);
        
        $this->collection_amount = $this->collecting_installment->installment_amount;
        $this->partial_payment = false;
        $this->collection_notes = '';
        $this->show_collection_modal = true;
    }

    public function closeCollectionModal()
    {
        $this->show_collection_modal = false;
        $this->collecting_installment = null;
        $this->collection_amount = '';
        $this->partial_payment = false;
        $this->collection_notes = '';
        $this->resetValidation();
    }

    public function updatedPartialPayment()
    {
        if (!$this->partial_payment && $this->collecting_installment) {
            $this->collection_amount = $this->collecting_installment->installment_amount;
        }
    }

    public function collectInstallment()
    {
        $this->validate();

        $collectionAmount = (float) $this->collection_amount;
        $installmentAmount = $this->collecting_installment->installment_amount;

        if ($collectionAmount > $installmentAmount) {
            $this->addError('collection_amount', 'Collection amount cannot exceed installment amount.');
            return;
        }

        DB::transaction(function () use ($collectionAmount, $installmentAmount) {
            $due = $installmentAmount - $collectionAmount;
            $status = $due <= 0 ? 'paid' : 'partial';

            $this->collecting_installment->update([
                'status' => $status,
                'collected_by' => Auth::id(),
                'collection_date' => now(),
                'due' => $due,
                'advance_paid' => $collectionAmount,
            ]);

            // Create collection record (you can add a collections table)
            // Collection::create([...]);
        });

        session()->flash('success', 'Installment collected successfully!');
        $this->closeCollectionModal();
        $this->loadQuickStats();
    }

    public function markAsOverdue($installmentId)
    {
        $installment = Installment::find($installmentId);
        if ($installment && $installment->status === 'pending' && $installment->installment_date < now()) {
            $installment->update(['status' => 'overdue']);
            session()->flash('success', 'Installment marked as overdue.');
        }
    }

    public function bulkCollect()
    {
        if (empty($this->selected_installments)) {
            session()->flash('error', 'Please select installments to collect.');
            return;
        }

        $count = 0;
        foreach ($this->selected_installments as $installmentId) {
            $installment = Installment::find($installmentId);
            if ($installment && $installment->status === 'pending') {
                $installment->update([
                    'status' => 'paid',
                    'collected_by' => Auth::id(),
                    'collection_date' => now(),
                    'due' => 0,
                ]);
                $count++;
            }
        }

        session()->flash('success', "Successfully collected {$count} installments.");
        $this->selected_installments = [];
        $this->select_all = false;
        $this->loadQuickStats();
    }

    public function updatedSelectAll()
    {
        if ($this->select_all) {
            $this->selected_installments = $this->getInstallmentsQuery()
                ->where('status', 'pending')
                ->pluck('id')
                ->toArray();
        } else {
            $this->selected_installments = [];
        }
    }

    public function render()
    {
        $installments = $this->getInstallmentsQuery()->paginate(15);
        
        return view('livewire.installment-collection', [
            'installments' => $installments,
        ]);
    }
}
