<?php

namespace App\Livewire;

use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\LoanApplication;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class FieldOfficerDashboard extends Component
{
    public $selectedDate;
    public $quickActionModal = false;
    
    // Performance Metrics
    public $personalMetrics = [];
    public $todaySchedule = [];
    public $overduePayments = [];
    public $recentActivities = [];
    public $monthlyPerformance = [];
    public $achievementBadges = [];
    public $collectionEfficiency = 0;
    public $pendingTasks = 0;

    public function mount()
    {
        $this->selectedDate = Carbon::now()->format('Y-m-d');
        $this->loadPersonalMetrics();
        $this->loadTodaySchedule();
        $this->loadOverduePayments();
        $this->loadRecentActivities();
        $this->loadMonthlyPerformance();
        $this->loadAchievementBadges();
        $this->calculateCollectionEfficiency();
        $this->countPendingTasks();
    }

    public function updatedSelectedDate()
    {
        $this->loadTodaySchedule();
    }

    public function loadPersonalMetrics()
    {
        $user = Auth::user();
        $currentMonth = Carbon::now()->startOfMonth();
        
        // Members assigned to this officer (in their branch)
        $totalMembers = Member::where('branch_id', $user->branch_id)
            ->where('is_active', true)
            ->count();
            
        // Members created by this officer
        $membersCreated = Member::where('created_by', $user->id)
            ->where('is_active', true)
            ->count();
            
        // Collections this month
        $monthlyCollections = Installment::where('collected_by', $user->id)
            ->where('collection_date', '>=', $currentMonth)
            ->where('status', 'paid')
            ->sum('installment_amount');
            
        // Target (this would typically come from a targets table)
        $monthlyTarget = 50000; // This should be dynamic based on assigned targets
        
        // Today's collections
        $todayCollections = Installment::where('collected_by', $user->id)
            ->whereDate('collection_date', today())
            ->where('status', 'paid')
            ->sum('installment_amount');
            
        // Active loans in officer's area
        $activeLoans = Loan::whereHas('loanApplication.member', function($q) use ($user) {
            $q->where('branch_id', $user->branch_id);
        })->whereHas('loanApplication', function($q) {
            $q->where('status', 'approved');
        })->count();

        $this->personalMetrics = [
            'total_members' => $totalMembers,
            'members_created' => $membersCreated,
            'monthly_collections' => $monthlyCollections,
            'monthly_target' => $monthlyTarget,
            'target_achievement' => $monthlyTarget > 0 ? round(($monthlyCollections / $monthlyTarget) * 100, 1) : 0,
            'today_collections' => $todayCollections,
            'active_loans' => $activeLoans,
        ];
    }

    public function loadTodaySchedule()
    {
        $user = Auth::user();
        $selectedDate = Carbon::parse($this->selectedDate);
        
        $this->todaySchedule = Installment::with(['loan.loanApplication.member'])
            ->whereDate('installment_date', $selectedDate)
            ->where('status', 'pending')
            ->whereHas('loan.loanApplication.member', function($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })
            ->orderBy('installment_date')
            ->limit(20)
            ->get()
            ->map(function($installment) {
                return [
                    'id' => $installment->id,
                    'member_name' => $installment->loan->loanApplication->member->name,
                    'member_id' => $installment->loan->loanApplication->member->member_id,
                    'amount' => $installment->installment_amount,
                    'phone' => $installment->loan->loanApplication->member->phone_number,
                    'address' => $installment->loan->loanApplication->member->present_address,
                    'installment_no' => $installment->installment_no,
                    'loan_amount' => $installment->loan->loan_amount,
                ];
            })
            ->toArray();
    }

    public function loadOverduePayments()
    {
        $user = Auth::user();
        
        $this->overduePayments = Installment::with(['loan.loanApplication.member'])
            ->where('status', 'overdue')
            ->whereHas('loan.loanApplication.member', function($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })
            ->orderBy('installment_date')
            ->limit(15)
            ->get()
            ->map(function($installment) {
                $daysPastDue = Carbon::now()->diffInDays($installment->installment_date);
                return [
                    'id' => $installment->id,
                    'member_name' => $installment->loan->loanApplication->member->name,
                    'member_id' => $installment->loan->loanApplication->member->member_id,
                    'amount' => $installment->installment_amount,
                    'days_overdue' => $daysPastDue,
                    'phone' => $installment->loan->loanApplication->member->phone_number,
                    'installment_date' => $installment->installment_date->format('M d, Y'),
                    'priority' => $daysPastDue > 30 ? 'high' : ($daysPastDue > 7 ? 'medium' : 'low'),
                ];
            })
            ->toArray();
    }

    public function loadRecentActivities()
    {
        $user = Auth::user();
        
        // Recent collections
        $recentCollections = Installment::with(['loan.loanApplication.member'])
            ->where('collected_by', $user->id)
            ->where('status', 'paid')
            ->where('collection_date', '>=', Carbon::now()->subDays(7))
            ->orderBy('collection_date', 'desc')
            ->limit(5)
            ->get()
            ->map(function($installment) {
                return [
                    'type' => 'collection',
                    'description' => "Collected ৳{$installment->installment_amount} from {$installment->loan->loanApplication->member->name}",
                    'time' => $installment->collection_date->diffForHumans(),
                    'amount' => $installment->installment_amount,
                ];
            });
            
        // Recent member registrations
        $recentMembers = Member::where('created_by', $user->id)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function($member) {
                return [
                    'type' => 'member',
                    'description' => "Registered new member: {$member->name}",
                    'time' => $member->created_at->diffForHumans(),
                    'amount' => null,
                ];
            });
            
        $this->recentActivities = $recentCollections->concat($recentMembers)
            ->sortByDesc('time')
            ->take(8)
            ->values()
            ->toArray();
    }

    public function loadMonthlyPerformance()
    {
        $user = Auth::user();
        $months = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $startOfMonth = $month->copy()->startOfMonth();
            $endOfMonth = $month->copy()->endOfMonth();
            
            $collections = Installment::where('collected_by', $user->id)
                ->where('status', 'paid')
                ->whereBetween('collection_date', [$startOfMonth, $endOfMonth])
                ->sum('installment_amount');
                
            $membersAdded = Member::where('created_by', $user->id)
                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->count();
                
            $months[] = [
                'month' => $month->format('M Y'),
                'collections' => $collections,
                'members' => $membersAdded,
            ];
        }
        
        $this->monthlyPerformance = $months;
    }

    public function loadAchievementBadges()
    {
        $user = Auth::user();
        $badges = [];
        
        // Collection badges
        $totalCollections = Installment::where('collected_by', $user->id)
            ->where('status', 'paid')
            ->sum('installment_amount');
            
        if ($totalCollections >= 100000) $badges[] = ['name' => 'Collection Champion', 'icon' => '🏆', 'color' => 'gold'];
        if ($totalCollections >= 50000) $badges[] = ['name' => 'Collection Expert', 'icon' => '⭐', 'color' => 'silver'];
        
        // Member registration badges
        $totalMembers = Member::where('created_by', $user->id)->count();
        if ($totalMembers >= 50) $badges[] = ['name' => 'Member Master', 'icon' => '👥', 'color' => 'blue'];
        if ($totalMembers >= 20) $badges[] = ['name' => 'Member Builder', 'icon' => '👤', 'color' => 'green'];
        
        // Efficiency badges
        if ($this->collectionEfficiency >= 95) $badges[] = ['name' => 'Efficiency Expert', 'icon' => '⚡', 'color' => 'purple'];
        
        $this->achievementBadges = $badges;
    }

    public function calculateCollectionEfficiency()
    {
        $user = Auth::user();
        $currentMonth = Carbon::now()->startOfMonth();
        
        $totalDue = Installment::whereHas('loan.loanApplication.member', function($q) use ($user) {
            $q->where('branch_id', $user->branch_id);
        })->where('installment_date', '>=', $currentMonth)
          ->where('installment_date', '<=', now())
          ->sum('installment_amount');
          
        $totalCollected = Installment::where('collected_by', $user->id)
            ->where('collection_date', '>=', $currentMonth)
            ->where('status', 'paid')
            ->sum('installment_amount');
            
        $this->collectionEfficiency = $totalDue > 0 ? round(($totalCollected / $totalDue) * 100, 1) : 0;
    }

    public function countPendingTasks()
    {
        $user = Auth::user();
        
        // Count overdue installments + today's pending + pending loan applications in branch
        $overdueCount = Installment::where('status', 'overdue')
            ->whereHas('loan.loanApplication.member', function($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })->count();
            
        $todayPending = Installment::whereDate('installment_date', today())
            ->where('status', 'pending')
            ->whereHas('loan.loanApplication.member', function($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })->count();
            
        $this->pendingTasks = $overdueCount + $todayPending;
    }

    public function collectInstallment($installmentId)
    {
        $installment = Installment::find($installmentId);
        if ($installment && $installment->status === 'pending') {
            $installment->markAsPaid(Auth::user());
            $this->loadTodaySchedule();
            $this->loadOverduePayments();
            $this->loadPersonalMetrics();
            $this->loadRecentActivities();
            session()->flash('success', 'Installment collected successfully!');
        }
    }

    public function markOverdue($installmentId)
    {
        $installment = Installment::find($installmentId);
        if ($installment && $installment->status === 'pending') {
            $installment->update(['status' => 'overdue']);
            $this->loadTodaySchedule();
            $this->loadOverduePayments();
            session()->flash('info', 'Installment marked as overdue.');
        }
    }

    public function render()
    {
        return view('livewire.field-officer-dashboard');
    }
}
