<?php

namespace App\Livewire;

use App\Models\LoanApplication as LoanApplicationModel;
use App\Models\Member;
use App\Services\LoanCalculatorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Carbon\Carbon;

class LoanApplication extends Component
{
    use WithFileUploads;

    // Form properties
    public $member_id;
    public $applied_amount;
    public $reason;
    public $recommender;
    public $advance_payment = 0;
    public $repayment_method = 'monthly';
    public $duration_months = 12;
    public $interest_rate = 15; // Default 15% annual interest
    
    // File uploads
    public $documents = [];
    public $uploaded_documents = [];
    
    // Calculation results
    public $loan_calculation = [];
    public $show_calculator = false;
    
    // Member selection
    public $members = [];
    public $selected_member = null;
    public $member_search = '';
    
    // Validation rules
    protected $rules = [
        'member_id' => 'required|exists:members,id',
        'applied_amount' => 'required|numeric|min:1000|max:500000',
        'reason' => 'required|string|min:10|max:500',
        'recommender' => 'required|string|min:3|max:100',
        'advance_payment' => 'nullable|numeric|min:0',
        'repayment_method' => 'required|in:weekly,monthly',
        'duration_months' => 'required|integer|min:3|max:60',
        'documents.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
    ];

    protected $messages = [
        'applied_amount.min' => 'Minimum loan amount is ৳1,000',
        'applied_amount.max' => 'Maximum loan amount is ৳5,00,000',
        'reason.min' => 'Please provide a detailed reason (at least 10 characters)',
        'documents.*.mimes' => 'Documents must be PDF, JPG, JPEG, or PNG files',
        'documents.*.max' => 'Each document must be less than 2MB',
    ];

    public function mount()
    {
        // If user is a member, auto-select their profile
        if (Auth::user()->role === 'member' && Auth::user()->member_id) {
            $this->member_id = Auth::user()->member_id;
            $this->selected_member = Auth::user()->memberProfile;
        }
        
        $this->loadMembers();
    }

    public function loadMembers()
    {
        $query = Member::where('is_active', true);
        
        // If user is not admin/manager, only show members from their branch
        if (!in_array(Auth::user()->role, ['admin', 'manager'])) {
            $query->where('branch_id', Auth::user()->branch_id);
        }
        
        if ($this->member_search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->member_search . '%')
                  ->orWhere('member_id', 'like', '%' . $this->member_search . '%')
                  ->orWhere('phone_number', 'like', '%' . $this->member_search . '%');
            });
        }
        
        $this->members = $query->limit(20)->get();
    }

    public function updatedMemberSearch()
    {
        $this->loadMembers();
    }

    public function selectMember($memberId)
    {
        $this->member_id = $memberId;
        $this->selected_member = Member::find($memberId);
        $this->member_search = '';
        $this->members = [];
    }

    public function updatedAppliedAmount()
    {
        $this->calculateLoan();
    }

    public function updatedAdvancePayment()
    {
        $this->calculateLoan();
    }

    public function updatedRepaymentMethod()
    {
        $this->calculateLoan();
    }

    public function updatedDurationMonths()
    {
        $this->calculateLoan();
    }

    public function calculateLoan()
    {
        if ($this->applied_amount && $this->applied_amount >= 1000) {
            $this->loan_calculation = LoanCalculatorService::calculateLoan(
                loanAmount: (float) $this->applied_amount,
                interestRate: $this->interest_rate,
                durationMonths: $this->duration_months,
                repaymentMethod: $this->repayment_method,
                advancePayment: (float) $this->advance_payment
            );
            $this->show_calculator = true;
        } else {
            $this->show_calculator = false;
        }
    }

    public function toggleCalculator()
    {
        $this->show_calculator = !$this->show_calculator;
        if ($this->show_calculator) {
            $this->calculateLoan();
        }
    }

    public function removeDocument($index)
    {
        unset($this->documents[$index]);
        $this->documents = array_values($this->documents);
    }

    public function submit()
    {
        $this->validate();

        // Check if member has pending applications
        $pendingApplications = LoanApplicationModel::where('member_id', $this->member_id)
            ->where('status', 'pending')
            ->count();

        if ($pendingApplications > 0) {
            $this->addError('member_id', 'This member already has a pending loan application.');
            return;
        }

        // Get member's loan cycle number
        $lastApprovedLoan = LoanApplicationModel::where('member_id', $this->member_id)
            ->where('status', 'approved')
            ->orderBy('loan_cycle_number', 'desc')
            ->first();

        $loanCycleNumber = $lastApprovedLoan ? $lastApprovedLoan->loan_cycle_number + 1 : 1;

        // Upload documents
        $documentPaths = [];
        foreach ($this->documents as $document) {
            $path = $document->store('loan-documents', 'public');
            $documentPaths[] = $path;
        }

        // Create loan application
        $application = LoanApplicationModel::create([
            'member_id' => $this->member_id,
            'applied_amount' => $this->applied_amount,
            'reason' => $this->reason,
            'loan_cycle_number' => $loanCycleNumber,
            'recommender' => $this->recommender,
            'advance_payment' => $this->advance_payment ?? 0,
            'applied_at' => now(),
        ]);

        // Store additional application data (documents, calculation details)
        $application->update([
            'documents' => json_encode($documentPaths),
            'calculation_details' => json_encode($this->loan_calculation),
        ]);

        session()->flash('success', 'Loan application submitted successfully! Application ID: ' . $application->id);
        
        $this->reset();
        $this->mount();
    }

    public function render()
    {
        return view('livewire.loan-application');
    }
}
