<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Installment;
use App\Models\BranchTransaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class BranchManagerDashboard extends Component
{
    use WithPagination;

    public $selectedPeriod = '30'; // days
    public $selectedOfficer = '';
    public $showApprovalModal = false;
    public $selectedApplication = null;
    
    // Dashboard Data
    public $branchOverview = [];
    public $officerPerformance = [];
    public $loanPortfolio = [];
    public $pendingApprovals = [];
    public $incomeExpenseSummary = [];
    public $collectionEfficiency = [];
    public $branchTargets = [];
    public $alertsNotifications = [];

    public function mount()
    {
        $this->loadBranchOverview();
        $this->loadOfficerPerformance();
        $this->loadLoanPortfolio();
        $this->loadPendingApprovals();
        $this->loadIncomeExpenseSummary();
        $this->loadCollectionEfficiency();
        $this->loadBranchTargets();
        $this->loadAlertsNotifications();
    }

    public function updatedSelectedPeriod()
    {
        $this->loadBranchOverview();
        $this->loadOfficerPerformance();
        $this->loadLoanPortfolio();
        $this->loadIncomeExpenseSummary();
        $this->loadCollectionEfficiency();
    }

    public function updatedSelectedOfficer()
    {
        $this->loadOfficerPerformance();
    }

    public function loadBranchOverview()
    {
        $user = Auth::user();
        $branch = $user->branch;
        $startDate = Carbon::now()->subDays($this->selectedPeriod);
        
        if (!$branch) {
            $this->branchOverview = [];
            return;
        }

        // Basic metrics
        $totalMembers = $branch->members()->where('is_active', true)->count();
        $totalOfficers = $branch->users()->where('role', 'field_officer')->where('is_active', true)->count();
        
        // Loan metrics
        $activeLoans = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->whereHas('loanApplication', function($q) {
            $q->where('status', 'approved');
        })->count();
        
        $totalLoanAmount = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('loan_date', '>=', $startDate)->sum('loan_amount');
        
        // Collection metrics
        $totalCollections = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('collection_date', '>=', $startDate)
          ->where('status', 'paid')
          ->sum('installment_amount');
          
        $overdueAmount = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('status', 'overdue')->sum('installment_amount');
        
        // Today's metrics
        $todayCollections = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->whereDate('collection_date', today())
          ->where('status', 'paid')
          ->sum('installment_amount');
          
        $newMembersToday = $branch->members()->whereDate('created_at', today())->count();

        $this->branchOverview = [
            'branch_name' => $branch->name,
            'total_members' => $totalMembers,
            'total_officers' => $totalOfficers,
            'active_loans' => $activeLoans,
            'total_loan_amount' => $totalLoanAmount,
            'total_collections' => $totalCollections,
            'overdue_amount' => $overdueAmount,
            'today_collections' => $todayCollections,
            'new_members_today' => $newMembersToday,
            'collection_rate' => $totalLoanAmount > 0 ? round(($totalCollections / $totalLoanAmount) * 100, 2) : 0,
        ];
    }

    public function loadOfficerPerformance()
    {
        $user = Auth::user();
        $branch = $user->branch;
        $startDate = Carbon::now()->subDays($this->selectedPeriod);
        
        if (!$branch) {
            $this->officerPerformance = [];
            return;
        }

        $query = $branch->users()->where('role', 'field_officer')->where('is_active', true);
        
        if ($this->selectedOfficer) {
            $query->where('id', $this->selectedOfficer);
        }
        
        $officers = $query->get();
        
        $this->officerPerformance = $officers->map(function($officer) use ($startDate) {
            $collections = Installment::where('collected_by', $officer->id)
                ->where('collection_date', '>=', $startDate)
                ->where('status', 'paid')
                ->sum('installment_amount');
                
            $membersCreated = Member::where('created_by', $officer->id)
                ->where('created_at', '>=', $startDate)
                ->count();
                
            $collectionsCount = Installment::where('collected_by', $officer->id)
                ->where('collection_date', '>=', $startDate)
                ->where('status', 'paid')
                ->count();
                
            // Calculate efficiency
            $assignedInstallments = Installment::whereHas('loan.loanApplication.member', function($q) use ($officer) {
                $q->where('branch_id', $officer->branch_id);
            })->where('installment_date', '>=', $startDate)
              ->where('installment_date', '<=', now())
              ->count();
              
            $efficiency = $assignedInstallments > 0 ? round(($collectionsCount / $assignedInstallments) * 100, 2) : 0;
            
            return [
                'id' => $officer->id,
                'name' => $officer->name,
                'collections' => $collections,
                'members_created' => $membersCreated,
                'collections_count' => $collectionsCount,
                'efficiency' => $efficiency,
                'last_activity' => $officer->updated_at->diffForHumans(),
            ];
        })->toArray();
    }

    public function loadLoanPortfolio()
    {
        $user = Auth::user();
        $branch = $user->branch;
        $startDate = Carbon::now()->subDays($this->selectedPeriod);
        
        if (!$branch) {
            $this->loanPortfolio = [];
            return;
        }

        // Portfolio analysis
        $totalPortfolio = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->whereHas('loanApplication', function($q) {
            $q->where('status', 'approved');
        })->sum('loan_amount');
        
        $outstandingAmount = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->whereIn('status', ['pending', 'overdue'])->sum('installment_amount');
        
        $paidAmount = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('status', 'paid')->sum('installment_amount');
        
        // Risk analysis
        $highRiskLoans = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->whereHas('installments', function($q) {
            $q->where('status', 'overdue')
              ->where('installment_date', '<', Carbon::now()->subDays(30));
        })->count();
        
        // New loans this period
        $newLoans = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('loan_date', '>=', $startDate)->count();
        
        $newLoanAmount = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('loan_date', '>=', $startDate)->sum('loan_amount');

        $this->loanPortfolio = [
            'total_portfolio' => $totalPortfolio,
            'outstanding_amount' => $outstandingAmount,
            'paid_amount' => $paidAmount,
            'recovery_rate' => $totalPortfolio > 0 ? round(($paidAmount / $totalPortfolio) * 100, 2) : 0,
            'high_risk_loans' => $highRiskLoans,
            'new_loans' => $newLoans,
            'new_loan_amount' => $newLoanAmount,
        ];
    }

    public function loadPendingApprovals()
    {
        $user = Auth::user();
        $branch = $user->branch;
        
        if (!$branch) {
            $this->pendingApprovals = [];
            return;
        }

        $this->pendingApprovals = LoanApplication::with(['member'])
            ->whereHas('member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })
            ->where('status', 'pending')
            ->orderBy('applied_at', 'asc')
            ->limit(10)
            ->get()
            ->map(function($application) {
                return [
                    'id' => $application->id,
                    'member_name' => $application->member->name,
                    'member_id' => $application->member->member_id,
                    'applied_amount' => $application->applied_amount,
                    'reason' => $application->reason,
                    'applied_at' => $application->applied_at->format('M d, Y'),
                    'days_pending' => $application->applied_at->diffInDays(now()),
                ];
            })
            ->toArray();
    }

    public function loadIncomeExpenseSummary()
    {
        $user = Auth::user();
        $branch = $user->branch;
        $startDate = Carbon::now()->subDays($this->selectedPeriod);
        
        if (!$branch) {
            $this->incomeExpenseSummary = [];
            return;
        }

        // Income from collections
        $collectionIncome = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('collection_date', '>=', $startDate)
          ->where('status', 'paid')
          ->sum('installment_amount');
          
        // Loan disbursements (expense)
        $loanDisbursements = Loan::whereHas('loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('loan_date', '>=', $startDate)->sum('loan_amount');
        
        // Branch transactions
        $branchIncome = BranchTransaction::where('branch_id', $branch->id)
            ->where('entry_type', 'income')
            ->where('date', '>=', $startDate)
            ->sum('amount');

        $branchExpenses = BranchTransaction::where('branch_id', $branch->id)
            ->where('entry_type', 'expense')
            ->where('date', '>=', $startDate)
            ->sum('amount');

        $netIncome = ($collectionIncome + $branchIncome) - ($loanDisbursements + $branchExpenses);

        $this->incomeExpenseSummary = [
            'collection_income' => $collectionIncome,
            'loan_disbursements' => $loanDisbursements,
            'branch_income' => $branchIncome,
            'branch_expenses' => $branchExpenses,
            'net_income' => $netIncome,
            'profit_margin' => $collectionIncome > 0 ? round((($collectionIncome - $loanDisbursements) / $collectionIncome) * 100, 2) : 0,
        ];
    }

    public function loadCollectionEfficiency()
    {
        $user = Auth::user();
        $branch = $user->branch;

        if (!$branch) {
            $this->collectionEfficiency = [];
            return;
        }

        $days = min(30, (int)$this->selectedPeriod);
        $efficiency = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);

            $due = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->whereDate('installment_date', $date)->sum('installment_amount');

            $collected = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->whereDate('collection_date', $date)
              ->where('status', 'paid')
              ->sum('installment_amount');

            $efficiency[] = [
                'date' => $date->format('M d'),
                'due' => $due,
                'collected' => $collected,
                'rate' => $due > 0 ? round(($collected / $due) * 100, 2) : 0,
            ];
        }

        $this->collectionEfficiency = $efficiency;
    }

    public function loadBranchTargets()
    {
        // This would typically come from a targets table
        // For now, we'll use static targets
        $this->branchTargets = [
            'monthly_collection_target' => 200000,
            'monthly_member_target' => 20,
            'monthly_loan_target' => 150000,
            'collection_efficiency_target' => 95,
        ];
    }

    public function loadAlertsNotifications()
    {
        $user = Auth::user();
        $branch = $user->branch;
        
        if (!$branch) {
            $this->alertsNotifications = [];
            return;
        }

        $alerts = [];
        
        // High overdue amounts
        $overdueAmount = Installment::whereHas('loan.loanApplication.member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('status', 'overdue')->sum('installment_amount');
        
        if ($overdueAmount > 50000) {
            $alerts[] = [
                'type' => 'warning',
                'message' => "High overdue amount: ৳" . number_format($overdueAmount, 0),
                'action' => 'Review overdue payments',
            ];
        }
        
        // Pending approvals
        $pendingCount = LoanApplication::whereHas('member', function($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->where('status', 'pending')->count();
        
        if ($pendingCount > 5) {
            $alerts[] = [
                'type' => 'info',
                'message' => "{$pendingCount} loan applications pending approval",
                'action' => 'Review applications',
            ];
        }
        
        // Low collection efficiency
        $avgEfficiency = collect($this->collectionEfficiency)->avg('rate');
        if ($avgEfficiency < 80) {
            $alerts[] = [
                'type' => 'error',
                'message' => "Collection efficiency below target: {$avgEfficiency}%",
                'action' => 'Improve collections',
            ];
        }
        
        $this->alertsNotifications = $alerts;
    }

    public function approveLoan($applicationId)
    {
        $application = LoanApplication::find($applicationId);
        if ($application && $application->status === 'pending') {
            $application->update([
                'status' => 'approved',
                'reviewed_by' => Auth::id(),
                'reviewed_at' => now(),
            ]);
            
            $this->loadPendingApprovals();
            session()->flash('success', 'Loan application approved successfully!');
        }
    }

    public function rejectLoan($applicationId)
    {
        $application = LoanApplication::find($applicationId);
        if ($application && $application->status === 'pending') {
            $application->update([
                'status' => 'rejected',
                'reviewed_by' => Auth::id(),
                'reviewed_at' => now(),
            ]);
            
            $this->loadPendingApprovals();
            session()->flash('info', 'Loan application rejected.');
        }
    }

    public function getFieldOfficers()
    {
        $user = Auth::user();
        $branch = $user->branch;
        
        if (!$branch) return collect();
        
        return $branch->users()->where('role', 'field_officer')->where('is_active', true)->get();
    }

    public function exportReport()
    {
        // Implementation for exporting branch report
        session()->flash('success', 'Branch report export functionality will be implemented.');
    }

    public function render()
    {
        return view('livewire.branch-manager-dashboard', [
            'fieldOfficers' => $this->getFieldOfficers(),
        ]);
    }
}
