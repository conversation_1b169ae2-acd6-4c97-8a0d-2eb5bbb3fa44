<?php

namespace App\Livewire;

use App\Models\Loan;
use App\Models\Installment;
use App\Services\LoanCalculatorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class LoanManagement extends Component
{
    use WithPagination;

    // Filters
    public $search = '';
    public $status_filter = 'active';
    public $branch_filter = '';
    public $overdue_filter = '';
    
    // Loan details modal
    public $show_loan_modal = false;
    public $selected_loan = null;
    public $loan_installments = [];
    
    // Early payment modal
    public $show_early_payment_modal = false;
    public $early_payment_amount = '';
    public $early_payment_discount = 0;
    public $early_payment_calculation = [];
    
    // Loan modification modal
    public $show_modification_modal = false;
    public $modification_type = '';
    public $modification_reason = '';
    public $new_installment_amount = '';
    public $extension_months = '';

    protected $rules = [
        'early_payment_amount' => 'required|numeric|min:1',
        'modification_reason' => 'required|string|min:10|max:500',
        'new_installment_amount' => 'required_if:modification_type,installment_change|numeric|min:100',
        'extension_months' => 'required_if:modification_type,extension|integer|min:1|max:12',
    ];

    public function getLoansQuery()
    {
        $query = Loan::with(['loanApplication.member', 'installments'])
            ->when($this->search, function ($q) {
                $q->whereHas('loanApplication.member', function ($memberQuery) {
                    $memberQuery->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('member_id', 'like', '%' . $this->search . '%')
                        ->orWhere('phone_number', 'like', '%' . $this->search . '%');
                });
            });

        // Branch filter for managers
        if (Auth::user()->role === 'manager') {
            $query->whereHas('loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', Auth::user()->branch_id);
            });
        } elseif ($this->branch_filter) {
            $query->whereHas('loanApplication.member', function ($memberQuery) {
                $memberQuery->where('branch_id', $this->branch_filter);
            });
        }

        // Status filter
        if ($this->status_filter === 'active') {
            $query->whereHas('installments', function ($installmentQuery) {
                $installmentQuery->where('status', 'pending');
            });
        } elseif ($this->status_filter === 'completed') {
            $query->whereDoesntHave('installments', function ($installmentQuery) {
                $installmentQuery->where('status', 'pending');
            });
        }

        // Overdue filter
        if ($this->overdue_filter === 'overdue') {
            $query->whereHas('installments', function ($installmentQuery) {
                $installmentQuery->where('status', 'pending')
                    ->where('installment_date', '<', now()->toDateString());
            });
        }

        return $query->orderBy('loan_date', 'desc');
    }

    public function openLoanModal($loanId)
    {
        $this->selected_loan = Loan::with(['loanApplication.member', 'installments.collector'])
            ->find($loanId);
        
        $this->loan_installments = $this->selected_loan->installments()
            ->orderBy('installment_no')
            ->get();
            
        $this->show_loan_modal = true;
    }

    public function closeLoanModal()
    {
        $this->show_loan_modal = false;
        $this->selected_loan = null;
        $this->loan_installments = [];
    }

    public function openEarlyPaymentModal($loanId)
    {
        $this->selected_loan = Loan::with(['loanApplication.member', 'installments'])
            ->find($loanId);
        
        $this->early_payment_amount = $this->selected_loan->remaining_balance;
        $this->calculateEarlyPayment();
        $this->show_early_payment_modal = true;
    }

    public function closeEarlyPaymentModal()
    {
        $this->show_early_payment_modal = false;
        $this->selected_loan = null;
        $this->early_payment_amount = '';
        $this->early_payment_discount = 0;
        $this->early_payment_calculation = [];
        $this->resetValidation();
    }

    public function updatedEarlyPaymentAmount()
    {
        $this->calculateEarlyPayment();
    }

    public function updatedEarlyPaymentDiscount()
    {
        $this->calculateEarlyPayment();
    }

    public function calculateEarlyPayment()
    {
        if ($this->selected_loan && $this->early_payment_amount) {
            $remainingInstallments = $this->selected_loan->installments()
                ->where('status', 'pending')
                ->get()
                ->toArray();

            $this->early_payment_calculation = LoanCalculatorService::calculateEarlyPaymentSavings(
                remainingBalance: (float) $this->early_payment_amount,
                remainingInstallments: $remainingInstallments,
                earlyPaymentDiscount: $this->early_payment_discount
            );
        }
    }

    public function processEarlyPayment()
    {
        $this->validate(['early_payment_amount' => 'required|numeric|min:1']);

        DB::transaction(function () {
            // Mark all pending installments as paid
            $this->selected_loan->installments()
                ->where('status', 'pending')
                ->update([
                    'status' => 'paid',
                    'collected_by' => Auth::id(),
                    'collection_date' => now(),
                    'due' => 0,
                ]);

            // Log the early payment transaction
            // You can add a loan_transactions table to track this
        });

        session()->flash('success', 'Early payment processed successfully!');
        $this->closeEarlyPaymentModal();
    }

    public function openModificationModal($loanId, $type)
    {
        $this->selected_loan = Loan::with(['loanApplication.member', 'installments'])
            ->find($loanId);
        
        $this->modification_type = $type;
        $this->modification_reason = '';
        
        if ($type === 'installment_change') {
            $this->new_installment_amount = $this->selected_loan->installment_amount;
        }
        
        $this->show_modification_modal = true;
    }

    public function closeModificationModal()
    {
        $this->show_modification_modal = false;
        $this->selected_loan = null;
        $this->modification_type = '';
        $this->modification_reason = '';
        $this->new_installment_amount = '';
        $this->extension_months = '';
        $this->resetValidation();
    }

    public function processModification()
    {
        $this->validate();

        DB::transaction(function () {
            if ($this->modification_type === 'installment_change') {
                // Recalculate remaining installments
                $pendingInstallments = $this->selected_loan->installments()
                    ->where('status', 'pending')
                    ->orderBy('installment_no')
                    ->get();

                $remainingBalance = $this->selected_loan->remaining_balance;
                $newInstallmentAmount = (float) $this->new_installment_amount;
                $newInstallmentCount = ceil($remainingBalance / $newInstallmentAmount);

                // Update existing pending installments
                foreach ($pendingInstallments as $index => $installment) {
                    if ($index < $newInstallmentCount) {
                        $installment->update([
                            'installment_amount' => $newInstallmentAmount,
                        ]);
                    } else {
                        $installment->delete();
                    }
                }

                // Create additional installments if needed
                if ($newInstallmentCount > $pendingInstallments->count()) {
                    $lastInstallment = $pendingInstallments->last();
                    $lastDate = Carbon::parse($lastInstallment->installment_date);
                    
                    for ($i = $pendingInstallments->count(); $i < $newInstallmentCount; $i++) {
                        $lastDate->add(1, $this->selected_loan->repayment_method === 'weekly' ? 'week' : 'month');
                        
                        Installment::create([
                            'loan_id' => $this->selected_loan->id,
                            'installment_no' => $lastInstallment->installment_no + $i + 1,
                            'installment_date' => $lastDate->copy(),
                            'installment_amount' => $newInstallmentAmount,
                            'status' => 'pending',
                        ]);
                    }
                }

                $this->selected_loan->update([
                    'installment_amount' => $newInstallmentAmount,
                    'installment_count' => $this->selected_loan->installment_count - $pendingInstallments->count() + $newInstallmentCount,
                ]);

            } elseif ($this->modification_type === 'extension') {
                // Extend loan duration
                $lastInstallment = $this->selected_loan->installments()
                    ->orderBy('installment_no', 'desc')
                    ->first();

                $extensionDate = Carbon::parse($lastInstallment->installment_date);
                $installmentAmount = $this->selected_loan->installment_amount;

                for ($i = 1; $i <= $this->extension_months; $i++) {
                    $extensionDate->add(1, $this->selected_loan->repayment_method === 'weekly' ? 'week' : 'month');
                    
                    Installment::create([
                        'loan_id' => $this->selected_loan->id,
                        'installment_no' => $lastInstallment->installment_no + $i,
                        'installment_date' => $extensionDate->copy(),
                        'installment_amount' => $installmentAmount,
                        'status' => 'pending',
                    ]);
                }

                $this->selected_loan->update([
                    'installment_count' => $this->selected_loan->installment_count + $this->extension_months,
                    'last_installment_date' => $extensionDate,
                ]);
            }

            // Log the modification
            // You can add a loan_modifications table to track this
        });

        session()->flash('success', 'Loan modification processed successfully!');
        $this->closeModificationModal();
    }

    public function closeLoan($loanId)
    {
        $loan = Loan::find($loanId);
        
        if ($loan && $loan->is_fully_paid) {
            // Mark loan as closed
            // You can add a status field to loans table
            session()->flash('success', 'Loan closed successfully!');
        } else {
            session()->flash('error', 'Cannot close loan with pending payments.');
        }
    }

    public function render()
    {
        $loans = $this->getLoansQuery()->paginate(10);
        
        return view('livewire.loan-management', [
            'loans' => $loans,
        ]);
    }
}
