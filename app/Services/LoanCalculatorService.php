<?php

namespace App\Services;

use Carbon\Carbon;

class LoanCalculatorService
{
    /**
     * Calculate loan details including repayment schedule
     */
    public static function calculateLoan(
        float $loanAmount,
        float $interestRate,
        int $durationMonths,
        string $repaymentMethod = 'monthly',
        float $advancePayment = 0,
        Carbon $startDate = null
    ): array {
        $startDate = $startDate ?? Carbon::now();
        
        // Calculate total repayment amount (simple interest)
        $totalInterest = ($loanAmount * $interestRate * $durationMonths) / 100;
        $totalRepaymentAmount = $loanAmount + $totalInterest;
        
        // Calculate installment details
        $installmentCount = $repaymentMethod === 'weekly' 
            ? $durationMonths * 4 
            : $durationMonths;
            
        $installmentAmount = ($totalRepaymentAmount - $advancePayment) / $installmentCount;
        
        // Generate payment schedule
        $schedule = self::generatePaymentSchedule(
            $installmentCount,
            $installmentAmount,
            $startDate,
            $repaymentMethod
        );
        
        return [
            'loan_amount' => $loanAmount,
            'total_repayment_amount' => $totalRepaymentAmount,
            'total_interest' => $totalInterest,
            'advance_payment' => $advancePayment,
            'installment_count' => $installmentCount,
            'installment_amount' => round($installmentAmount, 2),
            'repayment_method' => $repaymentMethod,
            'first_installment_date' => $schedule[0]['date'] ?? null,
            'last_installment_date' => $schedule[count($schedule) - 1]['date'] ?? null,
            'payment_schedule' => $schedule,
        ];
    }
    
    /**
     * Generate payment schedule
     */
    public static function generatePaymentSchedule(
        int $installmentCount,
        float $installmentAmount,
        Carbon $startDate,
        string $repaymentMethod
    ): array {
        $schedule = [];
        $currentDate = $startDate->copy();
        
        // Determine the interval based on repayment method
        $interval = $repaymentMethod === 'weekly' ? 'week' : 'month';
        
        for ($i = 1; $i <= $installmentCount; $i++) {
            if ($i > 1) {
                $currentDate->add(1, $interval);
            }
            
            $schedule[] = [
                'installment_no' => $i,
                'date' => $currentDate->copy(),
                'amount' => round($installmentAmount, 2),
                'status' => 'pending',
            ];
        }
        
        return $schedule;
    }
    
    /**
     * Calculate remaining balance for a loan
     */
    public static function calculateRemainingBalance(
        float $totalRepaymentAmount,
        array $paidInstallments
    ): float {
        $totalPaid = array_sum(array_column($paidInstallments, 'amount'));
        return max(0, $totalRepaymentAmount - $totalPaid);
    }
    
    /**
     * Calculate overdue amount
     */
    public static function calculateOverdueAmount(array $installments): float
    {
        $overdueAmount = 0;
        $today = Carbon::now()->toDateString();
        
        foreach ($installments as $installment) {
            if ($installment['status'] === 'pending' && $installment['date'] < $today) {
                $overdueAmount += $installment['amount'];
            }
        }
        
        return $overdueAmount;
    }
    
    /**
     * Get next due installment
     */
    public static function getNextDueInstallment(array $installments): ?array
    {
        $today = Carbon::now()->toDateString();
        
        foreach ($installments as $installment) {
            if ($installment['status'] === 'pending') {
                return $installment;
            }
        }
        
        return null;
    }
    
    /**
     * Calculate early payment savings
     */
    public static function calculateEarlyPaymentSavings(
        float $remainingBalance,
        array $remainingInstallments,
        float $earlyPaymentDiscount = 0
    ): array {
        $totalRemainingAmount = array_sum(array_column($remainingInstallments, 'amount'));
        $savings = $totalRemainingAmount - $remainingBalance;
        
        if ($earlyPaymentDiscount > 0) {
            $discountAmount = ($remainingBalance * $earlyPaymentDiscount) / 100;
            $savings += $discountAmount;
        }
        
        return [
            'remaining_balance' => $remainingBalance,
            'total_remaining_installments' => $totalRemainingAmount,
            'savings' => $savings,
            'discount_amount' => $discountAmount ?? 0,
            'final_payment_amount' => $remainingBalance - ($discountAmount ?? 0),
        ];
    }
}
