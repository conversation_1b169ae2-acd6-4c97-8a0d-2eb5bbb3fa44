<?php

namespace App\Services;

use App\Models\Member;
use App\Models\Installment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class NotificationService
{
    /**
     * Send SMS notification
     */
    public static function sendSMS(string $phoneNumber, string $message): bool
    {
        try {
            // Here you would integrate with SMS service provider
            // For example: Twilio, Nexmo, local SMS gateway, etc.
            
            // Example implementation with a hypothetical SMS service:
            /*
            $smsService = new SmsGateway();
            $response = $smsService->send([
                'to' => $phoneNumber,
                'message' => $message,
                'from' => config('sms.from_number')
            ]);
            
            return $response->isSuccessful();
            */
            
            // For now, we'll just log the SMS
            Log::info('SMS sent', [
                'phone' => $phoneNumber,
                'message' => $message,
                'timestamp' => now()
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('SMS sending failed', [
                'phone' => $phoneNumber,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Send Email notification
     */
    public static function sendEmail(string $email, string $subject, string $message): bool
    {
        try {
            // Here you would send actual email
            // For now, we'll just log the email
            Log::info('Email sent', [
                'email' => $email,
                'subject' => $subject,
                'message' => $message,
                'timestamp' => now()
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Email sending failed', [
                'email' => $email,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Send installment reminder to member
     */
    public static function sendInstallmentReminder(Installment $installment, string $customMessage = null): array
    {
        $member = $installment->loan->loanApplication->member;
        $results = ['sms' => false, 'email' => false];
        
        $message = $customMessage ?? self::getDefaultInstallmentReminderMessage($installment);
        
        // Send SMS
        if ($member->phone_number) {
            $results['sms'] = self::sendSMS($member->phone_number, $message);
        }
        
        // Send Email (if member has email)
        if (isset($member->email) && $member->email) {
            $subject = 'Loan Installment Reminder - ' . config('app.name');
            $results['email'] = self::sendEmail($member->email, $subject, $message);
        }
        
        return $results;
    }
    
    /**
     * Send bulk installment reminders
     */
    public static function sendBulkInstallmentReminders(array $installmentIds, string $customMessage = null): array
    {
        $installments = Installment::with(['loan.loanApplication.member'])
            ->whereIn('id', $installmentIds)
            ->get();
        
        $results = [
            'total' => $installments->count(),
            'sms_sent' => 0,
            'email_sent' => 0,
            'failed' => 0
        ];
        
        foreach ($installments as $installment) {
            $reminderResults = self::sendInstallmentReminder($installment, $customMessage);
            
            if ($reminderResults['sms']) {
                $results['sms_sent']++;
            }
            
            if ($reminderResults['email']) {
                $results['email_sent']++;
            }
            
            if (!$reminderResults['sms'] && !$reminderResults['email']) {
                $results['failed']++;
            }
        }
        
        return $results;
    }
    
    /**
     * Send loan approval notification
     */
    public static function sendLoanApprovalNotification(Member $member, float $loanAmount, string $status): bool
    {
        $message = self::getLoanApprovalMessage($member, $loanAmount, $status);
        
        $smsResult = false;
        $emailResult = false;
        
        // Send SMS
        if ($member->phone_number) {
            $smsResult = self::sendSMS($member->phone_number, $message);
        }
        
        // Send Email
        if (isset($member->email) && $member->email) {
            $subject = 'Loan Application ' . ucfirst($status) . ' - ' . config('app.name');
            $emailResult = self::sendEmail($member->email, $subject, $message);
        }
        
        return $smsResult || $emailResult;
    }
    
    /**
     * Send overdue payment notification
     */
    public static function sendOverdueNotification(Installment $installment): bool
    {
        $member = $installment->loan->loanApplication->member;
        $message = self::getOverduePaymentMessage($installment);
        
        $smsResult = false;
        $emailResult = false;
        
        // Send SMS
        if ($member->phone_number) {
            $smsResult = self::sendSMS($member->phone_number, $message);
        }
        
        // Send Email
        if (isset($member->email) && $member->email) {
            $subject = 'Overdue Payment Notice - ' . config('app.name');
            $emailResult = self::sendEmail($member->email, $subject, $message);
        }
        
        return $smsResult || $emailResult;
    }
    
    /**
     * Get default installment reminder message
     */
    private static function getDefaultInstallmentReminderMessage(Installment $installment): string
    {
        $member = $installment->loan->loanApplication->member;
        $amount = number_format($installment->installment_amount, 2);
        $dueDate = $installment->installment_date->format('M d, Y');
        
        return "Dear {$member->name}, your loan installment of ৳{$amount} is due on {$dueDate}. Please make the payment at your earliest convenience. Thank you.";
    }
    
    /**
     * Get loan approval message
     */
    private static function getLoanApprovalMessage(Member $member, float $loanAmount, string $status): string
    {
        $amount = number_format($loanAmount, 2);
        
        if ($status === 'approved') {
            return "Dear {$member->name}, congratulations! Your loan application for ৳{$amount} has been approved. Please visit our office to complete the formalities. Thank you.";
        } else {
            return "Dear {$member->name}, we regret to inform you that your loan application for ৳{$amount} has been rejected. Please contact our office for more details. Thank you.";
        }
    }
    
    /**
     * Get overdue payment message
     */
    private static function getOverduePaymentMessage(Installment $installment): string
    {
        $member = $installment->loan->loanApplication->member;
        $amount = number_format($installment->installment_amount, 2);
        $dueDate = $installment->installment_date->format('M d, Y');
        $daysPastDue = now()->diffInDays($installment->installment_date);
        
        return "Dear {$member->name}, your loan installment of ৳{$amount} was due on {$dueDate} ({$daysPastDue} days ago). Please make the payment immediately to avoid penalties. Thank you.";
    }
    
    /**
     * Send payment confirmation
     */
    public static function sendPaymentConfirmation(Installment $installment): bool
    {
        $member = $installment->loan->loanApplication->member;
        $amount = number_format($installment->installment_amount, 2);
        $collectionDate = $installment->collection_date->format('M d, Y');
        
        $message = "Dear {$member->name}, we have received your loan installment payment of ৳{$amount} on {$collectionDate}. Thank you for your payment.";
        
        $smsResult = false;
        
        // Send SMS confirmation
        if ($member->phone_number) {
            $smsResult = self::sendSMS($member->phone_number, $message);
        }
        
        return $smsResult;
    }
    
    /**
     * Schedule automated reminders
     */
    public static function scheduleAutomatedReminders(): void
    {
        // This method would be called by a scheduled job
        // to send automated reminders for due and overdue payments
        
        // Get installments due today
        $dueTodayInstallments = Installment::with(['loan.loanApplication.member'])
            ->where('status', 'pending')
            ->whereDate('installment_date', now()->toDateString())
            ->get();
        
        foreach ($dueTodayInstallments as $installment) {
            self::sendInstallmentReminder($installment);
        }
        
        // Get overdue installments (1, 3, 7 days overdue)
        $overdueInstallments = Installment::with(['loan.loanApplication.member'])
            ->where('status', 'pending')
            ->whereIn('installment_date', [
                now()->subDays(1)->toDateString(),
                now()->subDays(3)->toDateString(),
                now()->subDays(7)->toDateString(),
            ])
            ->get();
        
        foreach ($overdueInstallments as $installment) {
            self::sendOverdueNotification($installment);
        }
    }
}
