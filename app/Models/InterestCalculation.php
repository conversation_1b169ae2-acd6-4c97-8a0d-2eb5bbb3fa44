<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class InterestCalculation extends Model
{
    use HasFactory;

    protected $fillable = [
        'saving_account_id',
        'calculation_period',
        'period_start',
        'period_end',
        'principal_amount',
        'interest_rate',
        'calculated_interest',
        'tax_deducted',
        'net_interest',
        'posting_date',
        'status',
        'calculated_by',
        'posted_by',
        'calculation_details',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'posting_date' => 'date',
        'principal_amount' => 'decimal:2',
        'interest_rate' => 'decimal:2',
        'calculated_interest' => 'decimal:2',
        'tax_deducted' => 'decimal:2',
        'net_interest' => 'decimal:2',
        'calculation_details' => 'array',
    ];

    /**
     * Get the saving account that owns this calculation.
     */
    public function savingAccount(): BelongsTo
    {
        return $this->belongsTo(SavingAccount::class);
    }

    /**
     * Get the user who calculated the interest.
     */
    public function calculator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'calculated_by');
    }

    /**
     * Get the user who posted the interest.
     */
    public function poster(): BelongsTo
    {
        return $this->belongsTo(User::class, 'posted_by');
    }

    /**
     * Scope for calculated status.
     */
    public function scopeCalculated($query)
    {
        return $query->where('status', 'calculated');
    }

    /**
     * Scope for posted status.
     */
    public function scopePosted($query)
    {
        return $query->where('status', 'posted');
    }

    /**
     * Scope for specific period.
     */
    public function scopePeriod($query, $period)
    {
        return $query->where('calculation_period', $period);
    }

    /**
     * Calculate interest for a saving account.
     */
    public static function calculateInterest(SavingAccount $account, string $period, Carbon $startDate, Carbon $endDate): array
    {
        // Get the average balance for the period
        $averageBalance = self::calculateAverageBalance($account, $startDate, $endDate);

        // Get interest rate based on account type
        $interestRate = self::getInterestRate($account->saving_type);

        // Calculate days in period
        $days = $startDate->diffInDays($endDate) + 1;

        // Calculate interest (simple interest for now)
        $calculatedInterest = ($averageBalance * $interestRate * $days) / (365 * 100);

        // Calculate tax (10% on interest)
        $taxRate = 10; // 10% tax
        $taxDeducted = ($calculatedInterest * $taxRate) / 100;

        $netInterest = $calculatedInterest - $taxDeducted;

        return [
            'principal_amount' => $averageBalance,
            'interest_rate' => $interestRate,
            'calculated_interest' => round($calculatedInterest, 2),
            'tax_deducted' => round($taxDeducted, 2),
            'net_interest' => round($netInterest, 2),
            'calculation_details' => [
                'average_balance' => $averageBalance,
                'days' => $days,
                'tax_rate' => $taxRate,
                'calculation_method' => 'simple_interest',
            ],
        ];
    }

    /**
     * Calculate average balance for a period.
     */
    private static function calculateAverageBalance(SavingAccount $account, Carbon $startDate, Carbon $endDate): float
    {
        // Get all transactions in the period
        $transactions = SavingTransaction::where('saving_account_id', $account->id)
            ->where('transaction_date', '<=', $endDate)
            ->where('status', 'completed')
            ->orderBy('transaction_date')
            ->get();

        if ($transactions->isEmpty()) {
            return 0;
        }

        // Calculate daily balances
        $dailyBalances = [];
        $currentBalance = 0;

        // Get balance at start of period
        $balanceAtStart = SavingTransaction::where('saving_account_id', $account->id)
            ->where('transaction_date', '<', $startDate)
            ->where('status', 'completed')
            ->orderBy('transaction_date', 'desc')
            ->first();

        if ($balanceAtStart) {
            $currentBalance = $balanceAtStart->balance_after;
        }

        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            // Update balance for transactions on this date
            $dayTransactions = $transactions->where('transaction_date', $currentDate->toDateString());
            foreach ($dayTransactions as $transaction) {
                $currentBalance = $transaction->balance_after;
            }

            $dailyBalances[] = $currentBalance;
            $currentDate->addDay();
        }

        return count($dailyBalances) > 0 ? array_sum($dailyBalances) / count($dailyBalances) : 0;
    }

    /**
     * Get interest rate based on account type.
     */
    private static function getInterestRate(string $accountType): float
    {
        return match($accountType) {
            'general' => 5.0, // 5% annual
            'dps' => 8.0,     // 8% annual
            'fdr' => 10.0,    // 10% annual
            default => 5.0,
        };
    }

    /**
     * Post interest to account.
     */
    public function postInterest(User $user): bool
    {
        if ($this->status !== 'calculated') {
            return false;
        }

        // Create interest transaction
        SavingTransaction::create([
            'saving_account_id' => $this->saving_account_id,
            'transaction_type' => 'interest',
            'amount' => $this->net_interest,
            'balance_after' => $this->savingAccount->getCurrentBalance() + $this->net_interest,
            'transaction_date' => now()->toDateString(),
            'description' => "Interest for period {$this->period_start->format('M Y')} to {$this->period_end->format('M Y')}",
            'reference_no' => 'INT' . $this->id,
            'processed_by' => $user->id,
            'status' => 'completed',
        ]);

        // Update calculation status
        $this->update([
            'status' => 'posted',
            'posting_date' => now()->toDateString(),
            'posted_by' => $user->id,
        ]);

        return true;
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'calculated' => 'Calculated',
            'posted' => 'Posted',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get period label.
     */
    public function getPeriodLabelAttribute(): string
    {
        return match($this->calculation_period) {
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
            'yearly' => 'Yearly',
            default => ucfirst($this->calculation_period),
        };
    }
}
