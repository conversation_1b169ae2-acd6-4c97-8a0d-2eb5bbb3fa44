<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Installment extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_id',
        'installment_no',
        'installment_date',
        'installment_amount',
        'advance_paid',
        'due',
        'collected_by',
        'collection_date',
        'status',
    ];

    protected $casts = [
        'installment_date' => 'date',
        'collection_date' => 'datetime',
        'installment_amount' => 'decimal:2',
        'advance_paid' => 'decimal:2',
        'due' => 'decimal:2',
    ];

    /**
     * Get the loan that owns the installment.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the user who collected the installment.
     */
    public function collector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'collected_by');
    }

    /**
     * Get the member through the loan.
     */
    public function member(): BelongsTo
    {
        return $this->loan->loanApplication->member();
    }

    /**
     * Check if installment is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'pending' && 
               $this->installment_date < now()->toDateString();
    }

    /**
     * Get the actual paid amount.
     */
    public function getActualPaidAttribute(): float
    {
        return $this->installment_amount - $this->due;
    }

    /**
     * Mark installment as paid.
     */
    public function markAsPaid(User $collector, float $paidAmount = null): void
    {
        $paidAmount = $paidAmount ?? $this->installment_amount;
        
        $this->update([
            'status' => 'paid',
            'collected_by' => $collector->id,
            'collection_date' => now(),
            'due' => max(0, $this->installment_amount - $paidAmount),
        ]);
    }

    /**
     * Mark installment as overdue.
     */
    public function markAsOverdue(): void
    {
        if ($this->status === 'pending' && $this->installment_date < now()->toDateString()) {
            $this->update(['status' => 'overdue']);
        }
    }
}
