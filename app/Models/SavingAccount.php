<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class SavingAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id',
        'saving_type',
        'joint_photo',
        'nominee_name',
        'nominee_relation',
        'saving_method',
        'monthly_amount',
        'fdr_amount',
        'start_date',
        'created_by',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'monthly_amount' => 'decimal:2',
        'fdr_amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the member that owns the saving account.
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    /**
     * Get the user who created the saving account.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active accounts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for inactive accounts.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope for general savings.
     */
    public function scopeGeneral($query)
    {
        return $query->where('saving_type', 'general');
    }

    /**
     * Scope for DPS accounts.
     */
    public function scopeDps($query)
    {
        return $query->where('saving_type', 'dps');
    }

    /**
     * Scope for FDR accounts.
     */
    public function scopeFdr($query)
    {
        return $query->where('saving_type', 'fdr');
    }

    /**
     * Get the account number.
     */
    public function getAccountNumberAttribute(): string
    {
        return 'SAV-' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get the saving type label.
     */
    public function getSavingTypeLabelAttribute(): string
    {
        return match($this->saving_type) {
            'general' => 'General Savings',
            'dps' => 'Deposit Pension Scheme',
            'fdr' => 'Fixed Deposit Receipt',
            default => ucfirst($this->saving_type),
        };
    }

    /**
     * Get the saving method label.
     */
    public function getSavingMethodLabelAttribute(): string
    {
        return match($this->saving_method) {
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            default => ucfirst($this->saving_method),
        };
    }

    /**
     * Get all transactions for this account.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(SavingTransaction::class);
    }

    /**
     * Get all interest calculations for this account.
     */
    public function interestCalculations(): HasMany
    {
        return $this->hasMany(InterestCalculation::class);
    }

    /**
     * Get current balance of the account.
     */
    public function getCurrentBalance(): float
    {
        $lastTransaction = $this->transactions()
            ->where('status', 'completed')
            ->orderBy('transaction_date', 'desc')
            ->orderBy('id', 'desc')
            ->first();

        return $lastTransaction ? $lastTransaction->balance_after : 0;
    }

    /**
     * Get total deposits.
     */
    public function getTotalDeposits(): float
    {
        return $this->transactions()
            ->where('transaction_type', 'deposit')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Get total withdrawals.
     */
    public function getTotalWithdrawals(): float
    {
        return $this->transactions()
            ->where('transaction_type', 'withdrawal')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Get total interest earned.
     */
    public function getTotalInterest(): float
    {
        return $this->transactions()
            ->where('transaction_type', 'interest')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Process deposit transaction.
     */
    public function deposit(float $amount, User $processor, string $description = null): SavingTransaction
    {
        $currentBalance = $this->getCurrentBalance();
        $newBalance = $currentBalance + $amount;

        return SavingTransaction::create([
            'saving_account_id' => $this->id,
            'transaction_type' => 'deposit',
            'amount' => $amount,
            'balance_after' => $newBalance,
            'transaction_date' => now()->toDateString(),
            'description' => $description ?? 'Deposit',
            'reference_no' => SavingTransaction::generateReferenceNo(),
            'processed_by' => $processor->id,
            'status' => 'completed',
        ]);
    }

    /**
     * Process withdrawal transaction.
     */
    public function withdraw(float $amount, User $processor, string $description = null): SavingTransaction
    {
        $currentBalance = $this->getCurrentBalance();

        if ($currentBalance < $amount) {
            throw new \Exception('Insufficient balance for withdrawal');
        }

        $newBalance = $currentBalance - $amount;

        return SavingTransaction::create([
            'saving_account_id' => $this->id,
            'transaction_type' => 'withdrawal',
            'amount' => $amount,
            'balance_after' => $newBalance,
            'transaction_date' => now()->toDateString(),
            'description' => $description ?? 'Withdrawal',
            'reference_no' => SavingTransaction::generateReferenceNo(),
            'processed_by' => $processor->id,
            'status' => 'completed',
        ]);
    }

    /**
     * Check if account is eligible for interest calculation.
     */
    public function isEligibleForInterest(): bool
    {
        // Account must be active and have minimum balance
        return $this->is_active && $this->getCurrentBalance() >= 100;
    }

    /**
     * Get maturity date for FDR accounts.
     */
    public function getMaturityDate(): ?Carbon
    {
        if ($this->saving_type !== 'fdr') {
            return null;
        }

        // Assuming FDR has 1 year maturity
        return $this->start_date->addYear();
    }

    /**
     * Check if FDR account is matured.
     */
    public function getIsMaturedAttribute(): bool
    {
        if ($this->saving_type !== 'fdr') {
            return false;
        }

        $maturityDate = $this->getMaturityDate();
        return $maturityDate && $maturityDate <= now();
    }

    /**
     * Get account status based on activity and balance.
     */
    public function getAccountStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Closed';
        }

        $balance = $this->getCurrentBalance();
        if ($balance == 0) {
            return 'Zero Balance';
        }

        // Check for dormant account (no transaction in last 6 months)
        $lastTransaction = $this->transactions()
            ->where('status', 'completed')
            ->orderBy('transaction_date', 'desc')
            ->first();

        if (!$lastTransaction || $lastTransaction->transaction_date < now()->subMonths(6)) {
            return 'Dormant';
        }

        return 'Active';
    }
}
