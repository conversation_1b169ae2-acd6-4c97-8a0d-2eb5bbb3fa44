<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ScheduledReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_id',
        'name',
        'frequency',
        'recipients',
        'filters',
        'schedule_time',
        'schedule_day',
        'next_run_date',
        'last_run_date',
        'status',
        'last_error',
        'created_by',
    ];

    protected $casts = [
        'recipients' => 'array',
        'filters' => 'array',
        'schedule_time' => 'datetime:H:i:s',
        'next_run_date' => 'date',
        'last_run_date' => 'date',
    ];

    /**
     * Get the template for this scheduled report.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(ReportTemplate::class, 'template_id');
    }

    /**
     * Get the user who created this scheduled report.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active scheduled reports.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for reports due to run.
     */
    public function scopeDueToRun($query)
    {
        return $query->where('status', 'active')
                    ->where('next_run_date', '<=', now()->toDateString());
    }

    /**
     * Get available frequencies.
     */
    public static function getFrequencies(): array
    {
        return [
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
            'yearly' => 'Yearly',
        ];
    }

    /**
     * Get frequency label.
     */
    public function getFrequencyLabelAttribute(): string
    {
        $frequencies = self::getFrequencies();
        return $frequencies[$this->frequency] ?? ucfirst($this->frequency);
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'paused' => 'Paused',
            'completed' => 'Completed',
            'failed' => 'Failed',
            default => ucfirst($this->status),
        };
    }

    /**
     * Calculate next run date based on frequency.
     */
    public function calculateNextRunDate(): Carbon
    {
        $baseDate = $this->last_run_date ? Carbon::parse($this->last_run_date) : now();

        return match($this->frequency) {
            'daily' => $baseDate->addDay(),
            'weekly' => $baseDate->addWeek(),
            'monthly' => $baseDate->addMonth(),
            'quarterly' => $baseDate->addMonths(3),
            'yearly' => $baseDate->addYear(),
            default => $baseDate->addDay(),
        };
    }

    /**
     * Mark report as completed and calculate next run.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'last_run_date' => now()->toDateString(),
            'next_run_date' => $this->calculateNextRunDate()->toDateString(),
            'status' => 'active',
            'last_error' => null,
        ]);
    }

    /**
     * Mark report as failed.
     */
    public function markAsFailed(string $error): void
    {
        $this->update([
            'status' => 'failed',
            'last_error' => $error,
        ]);
    }

    /**
     * Pause the scheduled report.
     */
    public function pause(): void
    {
        $this->update(['status' => 'paused']);
    }

    /**
     * Resume the scheduled report.
     */
    public function resume(): void
    {
        $this->update([
            'status' => 'active',
            'next_run_date' => $this->calculateNextRunDate()->toDateString(),
        ]);
    }

    /**
     * Check if report is due to run now.
     */
    public function isDueToRun(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $now = now();
        $runDate = Carbon::parse($this->next_run_date);
        $runTime = Carbon::parse($this->schedule_time);

        return $runDate->toDateString() <= $now->toDateString() &&
               $runTime->format('H:i') <= $now->format('H:i');
    }

    /**
     * Execute the scheduled report.
     */
    public function execute(): bool
    {
        try {
            $reportData = $this->template->generateReport($this->filters ?? []);

            // Generate and send report
            $this->generateAndSendReport($reportData);

            $this->markAsCompleted();
            return true;
        } catch (\Exception $e) {
            $this->markAsFailed($e->getMessage());
            return false;
        }
    }

    private function generateAndSendReport(array $data): void
    {
        // Implementation for generating and sending report
        // This would integrate with PDF/Excel generation and email sending
    }
}
