<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Branch extends Model
{
    protected $fillable = [
        'name',
        'address',
        'manager_id',
    ];

    /**
     * Get the manager of the branch
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get all users assigned to this branch
     */
    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all members of this branch
     */
    public function members(): HasMany
    {
        return $this->hasMany(Member::class);
    }

    /**
     * Get all transactions for this branch
     */
    public function transactions(): Has<PERSON><PERSON>
    {
        return $this->hasMany(BranchTransaction::class);
    }
}
