<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class LoanApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id',
        'applied_amount',
        'reason',
        'loan_cycle_number',
        'recommender',
        'advance_payment',
        'status',
        'reviewed_by',
        'reviewed_at',
        'applied_at',
        'documents',
        'calculation_details',
        'review_comments',
    ];

    protected $casts = [
        'applied_amount' => 'decimal:2',
        'advance_payment' => 'decimal:2',
        'reviewed_at' => 'datetime',
        'applied_at' => 'datetime',
        'documents' => 'array',
        'calculation_details' => 'array',
    ];

    /**
     * Get the member that owns the loan application.
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    /**
     * Get the user who reviewed the application.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the loan associated with this application.
     */
    public function loan(): HasOne
    {
        return $this->hasOne(Loan::class);
    }

    /**
     * Scope for pending applications.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved applications.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected applications.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Approve the loan application.
     */
    public function approve(User $reviewer): void
    {
        $this->update([
            'status' => 'approved',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
        ]);
    }

    /**
     * Reject the loan application.
     */
    public function reject(User $reviewer): void
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
        ]);
    }

    /**
     * Check if application is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if application is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if application is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Get the application number.
     */
    public function getApplicationNumberAttribute(): string
    {
        return 'LA-' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }
}
