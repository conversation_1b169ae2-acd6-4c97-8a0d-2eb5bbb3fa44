<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ReportTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'description',
        'configuration',
        'default_filters',
        'export_format',
        'is_public',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'configuration' => 'array',
        'default_filters' => 'array',
        'is_public' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all scheduled reports using this template.
     */
    public function scheduledReports(): HasMany
    {
        return $this->hasMany(ScheduledReport::class, 'template_id');
    }

    /**
     * Scope for active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for public templates.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get available report types.
     */
    public static function getReportTypes(): array
    {
        return [
            'financial' => 'Financial Reports',
            'loan' => 'Loan Reports',
            'member' => 'Member Reports',
            'collection' => 'Collection Reports',
            'analytics' => 'Analytics Reports',
            'performance' => 'Performance Reports',
            'compliance' => 'Compliance Reports',
        ];
    }

    /**
     * Get available export formats.
     */
    public static function getExportFormats(): array
    {
        return [
            'pdf' => 'PDF',
            'excel' => 'Excel',
            'csv' => 'CSV',
        ];
    }

    /**
     * Get type label.
     */
    public function getTypeLabelAttribute(): string
    {
        $types = self::getReportTypes();
        return $types[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get export format label.
     */
    public function getExportFormatLabelAttribute(): string
    {
        $formats = self::getExportFormats();
        return $formats[$this->export_format] ?? strtoupper($this->export_format);
    }

    /**
     * Check if template can be edited by user.
     */
    public function canBeEditedBy(User $user): bool
    {
        return $this->created_by === $user->id || $user->role === 'admin';
    }

    /**
     * Generate report data based on configuration.
     */
    public function generateReport(array $filters = []): array
    {
        $mergedFilters = array_merge($this->default_filters ?? [], $filters);

        // This would be implemented based on report type
        switch ($this->type) {
            case 'financial':
                return $this->generateFinancialReport($mergedFilters);
            case 'loan':
                return $this->generateLoanReport($mergedFilters);
            case 'member':
                return $this->generateMemberReport($mergedFilters);
            case 'collection':
                return $this->generateCollectionReport($mergedFilters);
            case 'analytics':
                return $this->generateAnalyticsReport($mergedFilters);
            default:
                return [];
        }
    }

    private function generateFinancialReport(array $filters): array
    {
        // Implementation would go here
        return [];
    }

    private function generateLoanReport(array $filters): array
    {
        // Implementation would go here
        return [];
    }

    private function generateMemberReport(array $filters): array
    {
        // Implementation would go here
        return [];
    }

    private function generateCollectionReport(array $filters): array
    {
        // Implementation would go here
        return [];
    }

    private function generateAnalyticsReport(array $filters): array
    {
        // Implementation would go here
        return [];
    }
}
