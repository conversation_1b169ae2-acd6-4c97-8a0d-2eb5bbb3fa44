<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Advertisement extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'image',
        'link_url',
        'active',
        'display_order',
        'start_date',
        'end_date',
        'click_count',
        'impression_count',
        'target_audience',
        'placement',
        'created_by',
    ];

    protected $casts = [
        'active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'click_count' => 'integer',
        'impression_count' => 'integer',
    ];

    /**
     * Scope for active advertisements.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope for inactive advertisements.
     */
    public function scopeInactive($query)
    {
        return $query->where('active', false);
    }

    /**
     * Get the full image URL.
     */
    public function getImageUrlAttribute(): string
    {
        if (!$this->image) {
            return asset('images/placeholder-ad.png');
        }

        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        return Storage::disk('public')->url($this->image);
    }

    /**
     * Get the image path for storage.
     */
    public function getImagePathAttribute(): string
    {
        return config('filesystems.uploads.advertisements_path') . '/' . $this->image;
    }

    /**
     * Get the user who created this advertisement.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for scheduled advertisements.
     */
    public function scopeScheduled($query)
    {
        return $query->where('start_date', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    /**
     * Scope for specific placement.
     */
    public function scopePlacement($query, $placement)
    {
        return $query->where('placement', $placement);
    }

    /**
     * Check if advertisement is currently active and scheduled.
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->active) {
            return false;
        }

        $now = now();

        if ($this->start_date && $this->start_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        return true;
    }

    /**
     * Record an impression.
     */
    public function recordImpression(): void
    {
        $this->increment('impression_count');
    }

    /**
     * Record a click.
     */
    public function recordClick(): void
    {
        $this->increment('click_count');
    }

    /**
     * Get click-through rate.
     */
    public function getClickThroughRateAttribute(): float
    {
        if ($this->impression_count == 0) {
            return 0;
        }

        return ($this->click_count / $this->impression_count) * 100;
    }

    /**
     * Get available placements.
     */
    public static function getPlacements(): array
    {
        return [
            'login_page' => 'Login Page',
            'dashboard' => 'Dashboard',
            'sidebar' => 'Sidebar',
            'header' => 'Header',
            'footer' => 'Footer',
        ];
    }

    /**
     * Get placement label.
     */
    public function getPlacementLabelAttribute(): string
    {
        $placements = self::getPlacements();
        return $placements[$this->placement] ?? ucfirst($this->placement);
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        if (!$this->active) {
            return 'Inactive';
        }

        if (!$this->isCurrentlyActive()) {
            return 'Scheduled';
        }

        return 'Active';
    }

    /**
     * Delete the image file when the advertisement is deleted.
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($advertisement) {
            if ($advertisement->image && Storage::disk('public')->exists($advertisement->image)) {
                Storage::disk('public')->delete($advertisement->image);
            }
        });
    }
}
