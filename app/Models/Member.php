<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Member extends Model
{
    protected $fillable = [
        'member_id',
        'name',
        'father_or_husband_name',
        'mother_name',
        'present_address',
        'permanent_address',
        'nid_number',
        'date_of_birth',
        'religion',
        'phone_number',
        'blood_group',
        'photo',
        'occupation',
        'reference_id',
        'branch_id',
        'created_by',
        'is_active',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the branch this member belongs to
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the user who created this member
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the reference member
     */
    public function reference(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'reference_id');
    }

    /**
     * Get members who reference this member
     */
    public function referencedMembers(): HasMany
    {
        return $this->hasMany(Member::class, 'reference_id');
    }

    /**
     * Get all loan applications for this member
     */
    public function loanApplications(): HasMany
    {
        return $this->hasMany(LoanApplication::class);
    }

    /**
     * Get all saving accounts for this member
     */
    public function savingAccounts(): HasMany
    {
        return $this->hasMany(SavingAccount::class);
    }

    /**
     * Get the user account associated with this member
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id', 'member_id');
    }

    /**
     * Get active loans for this member
     */
    public function activeLoans()
    {
        return $this->hasManyThrough(
            Loan::class,
            LoanApplication::class,
            'member_id', // Foreign key on loan_applications table
            'loan_application_id', // Foreign key on loans table
            'id', // Local key on members table
            'id' // Local key on loan_applications table
        )->where('loan_applications.status', 'approved');
    }

    /**
     * Get pending loan applications
     */
    public function pendingLoanApplications(): HasMany
    {
        return $this->loanApplications()->where('status', 'pending');
    }

    /**
     * Get active saving accounts
     */
    public function activeSavingAccounts(): HasMany
    {
        return $this->savingAccounts()->where('is_active', true);
    }

    /**
     * Scope for active members
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for members by branch
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Generate unique member ID
     */
    public static function generateMemberId($branchId = null): string
    {
        $prefix = 'MEM';
        if ($branchId) {
            $branch = \App\Models\Branch::find($branchId);
            if ($branch) {
                $prefix = strtoupper(substr($branch->name, 0, 3));
            }
        }

        $lastMember = self::where('member_id', 'like', $prefix . '%')
            ->orderBy('member_id', 'desc')
            ->first();

        if ($lastMember) {
            $lastNumber = (int) substr($lastMember->member_id, strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get member's full name with father/husband name
     */
    public function getFullNameAttribute(): string
    {
        return $this->name . ' (S/O: ' . $this->father_or_husband_name . ')';
    }

    /**
     * Get member's age
     */
    public function getAgeAttribute(): int
    {
        return $this->date_of_birth->age;
    }

    /**
     * Get member's photo URL
     */
    public function getPhotoUrlAttribute(): string
    {
        if ($this->photo) {
            return \Storage::disk('public')->url($this->photo);
        }

        return asset('images/default-avatar.png');
    }

    /**
     * Get total savings balance
     */
    public function getTotalSavingsAttribute(): float
    {
        return $this->activeSavingAccounts()->sum('monthly_amount') ?? 0;
    }

    /**
     * Get total loan amount
     */
    public function getTotalLoanAmountAttribute(): float
    {
        return $this->loanApplications()
            ->where('status', 'approved')
            ->whereHas('loan')
            ->with('loan')
            ->get()
            ->sum(function ($application) {
                return $application->loan->loan_amount ?? 0;
            });
    }

    /**
     * Get total outstanding loan balance
     */
    public function getOutstandingLoanBalanceAttribute(): float
    {
        $totalLoanAmount = $this->total_loan_amount;
        $totalPaid = 0;

        $approvedApplications = $this->loanApplications()
            ->where('status', 'approved')
            ->whereHas('loan')
            ->with(['loan.installments' => function ($query) {
                $query->where('status', 'paid');
            }])
            ->get();

        foreach ($approvedApplications as $application) {
            if ($application->loan) {
                $totalPaid += $application->loan->installments->sum('installment_amount');
            }
        }

        return $totalLoanAmount - $totalPaid;
    }

    /**
     * Check if member has any overdue installments
     */
    public function hasOverdueInstallments(): bool
    {
        return $this->loanApplications()
            ->where('status', 'approved')
            ->whereHas('loan.installments', function ($query) {
                $query->where('status', 'overdue');
            })
            ->exists();
    }
}
