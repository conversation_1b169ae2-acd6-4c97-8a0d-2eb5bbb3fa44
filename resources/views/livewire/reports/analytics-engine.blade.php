<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Analytics Engine</h2>
                <p class="text-gray-600">Advanced analytics and performance insights</p>
            </div>
            <button wire:click="exportAnalytics"
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Export Analytics</span>
            </button>
        </div>
    </div>

    <!-- Analytics Configuration -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Analytics Configuration</h3>

        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Analytics Type</label>
                <select wire:model="analytics_type"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="installment_trends">Installment Trends</option>
                    <option value="default_rates">Default Rate Analysis</option>
                    <option value="portfolio_risk">Portfolio Risk Assessment</option>
                    <option value="member_behavior">Member Behavior Analytics</option>
                    <option value="officer_performance">Officer Performance</option>
                    <option value="branch_profitability">Branch Profitability</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" wire:model="date_from"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" wire:model="date_to"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Branch Filter</label>
                <select wire:model="branch_filter"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Branches</option>
                    @foreach($branches as $branch)
                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                    @endforeach
                </select>
            </div>

            <div class="flex items-end space-x-2">
                <button wire:click="generateAnalytics"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md">
                    Generate Analytics
                </button>
                <button wire:click="clearFilters"
                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                    Clear
                </button>
            </div>
        </div>
    </div>

    <!-- KPI Metrics -->
    @if(!empty($kpi_metrics))
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            @if($analytics_type === 'installment_trends')
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-blue-600">Collection Rate</p>
                            <p class="text-2xl font-bold text-blue-900">{{ number_format($kpi_metrics['overall_collection_rate'], 1) }}%</p>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-green-600">Total Collected</p>
                            <p class="text-2xl font-bold text-green-900">৳{{ number_format($kpi_metrics['total_collected'], 0) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-yellow-600">Overdue Rate</p>
                            <p class="text-2xl font-bold text-yellow-900">{{ number_format($kpi_metrics['overdue_percentage'], 1) }}%</p>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-purple-600">Total Installments</p>
                            <p class="text-2xl font-bold text-purple-900">{{ number_format($kpi_metrics['total_installments']) }}</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    @endif

    <!-- Insights Section -->
    @if(!empty($insights))
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Key Insights</h3>

            <div class="space-y-3">
                @foreach($insights as $insight)
                    <div class="flex items-start p-4 rounded-lg
                              {{ $insight['type'] === 'success' ? 'bg-green-50 border border-green-200' :
                                 ($insight['type'] === 'warning' ? 'bg-yellow-50 border border-yellow-200' : 'bg-red-50 border border-red-200') }}">
                        <div class="flex-shrink-0">
                            @if($insight['type'] === 'success')
                                <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            @elseif($insight['type'] === 'warning')
                                <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium
                                     {{ $insight['type'] === 'success' ? 'text-green-800' :
                                        ($insight['type'] === 'warning' ? 'text-yellow-800' : 'text-red-800') }}">
                                {{ $insight['title'] }}
                            </h4>
                            <p class="text-sm
                                    {{ $insight['type'] === 'success' ? 'text-green-700' :
                                       ($insight['type'] === 'warning' ? 'text-yellow-700' : 'text-red-700') }}">
                                {{ $insight['message'] }}
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Charts and Data Visualization -->
    @if(!empty($chart_data))
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Chart Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Trend Analysis</h3>
                <div class="h-80">
                    <canvas id="analyticsChart"></canvas>
                </div>
            </div>

            <!-- Data Table -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Detailed Data</h3>
                <div class="overflow-y-auto max-h-80">
                    @if($analytics_type === 'installment_trends' && !empty($analytics_data))
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Month</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Collection Rate</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach($analytics_data as $row)
                                    <tr>
                                        <td class="px-4 py-2 text-sm text-gray-900">{{ $row['month'] }}</td>
                                        <td class="px-4 py-2 text-sm {{ $row['collection_rate'] >= 80 ? 'text-green-600' : 'text-red-600' }}">
                                            {{ number_format($row['collection_rate'], 1) }}%
                                        </td>
                                        <td class="px-4 py-2 text-sm text-gray-900">৳{{ number_format($row['total_collected'], 0) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @elseif($analytics_type === 'officer_performance' && !empty($analytics_data))
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Officer</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Efficiency</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Collections</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach($analytics_data as $row)
                                    <tr>
                                        <td class="px-4 py-2 text-sm text-gray-900">{{ $row['officer_name'] }}</td>
                                        <td class="px-4 py-2 text-sm {{ $row['efficiency_rate'] >= 70 ? 'text-green-600' : 'text-red-600' }}">
                                            {{ number_format($row['efficiency_rate'], 1) }}%
                                        </td>
                                        <td class="px-4 py-2 text-sm text-gray-900">৳{{ number_format($row['total_collected'], 0) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @endif
                </div>
            </div>
        </div>
    @endif
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('analyticsGenerated', (data) => {
            updateAnalyticsChart(data);
        });
    });

    function updateAnalyticsChart(data) {
        const ctx = document.getElementById('analyticsChart');
        if (!ctx || !data.chart_data) return;

        // Clear existing chart
        if (window.analyticsChart) {
            window.analyticsChart.destroy();
        }

        const chartData = data.chart_data;

        if (data.analytics_type === 'installment_trends') {
            window.analyticsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.labels,
                    datasets: [
                        {
                            label: 'Collection Rate (%)',
                            data: chartData.collection_rates,
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Amount Collected',
                            data: chartData.amounts_collected,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Collection Rate (%)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Amount (৳)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        } else if (data.analytics_type === 'officer_performance') {
            window.analyticsChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: chartData.labels,
                    datasets: [
                        {
                            label: 'Efficiency Rate (%)',
                            data: chartData.efficiency_rates,
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: 'rgb(59, 130, 246)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Efficiency Rate (%)'
                            }
                        }
                    }
                }
            });
        }
    }
</script>
@endpush
