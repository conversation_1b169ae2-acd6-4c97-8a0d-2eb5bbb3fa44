<div class="space-y-6">
    <!-- Mobile-First Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-4 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-semibold">Welcome, {{ Auth::user()->name }}</h2>
                <p class="text-blue-100 text-sm">{{ Carbon\Carbon::now()->format('l, F j, Y') }}</p>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold">৳{{ number_format($personalMetrics['today_collections'], 0) }}</div>
                <div class="text-blue-100 text-sm">Today's Collections</div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics Cards -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <div class="text-lg font-semibold text-gray-900">{{ $personalMetrics['total_members'] }}</div>
                    <div class="text-xs text-gray-500">Total Members</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <div class="text-lg font-semibold text-gray-900">৳{{ number_format($personalMetrics['monthly_collections'], 0) }}</div>
                    <div class="text-xs text-gray-500">This Month</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <div class="text-lg font-semibold text-gray-900">{{ $personalMetrics['target_achievement'] }}%</div>
                    <div class="text-xs text-gray-500">Target Achievement</div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <div class="text-lg font-semibold text-gray-900">{{ $pendingTasks }}</div>
                    <div class="text-xs text-gray-500">Pending Tasks</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Collection Efficiency Meter -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Collection Efficiency</h3>
        <div class="flex items-center">
            <div class="flex-1">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                    <span>Current Efficiency</span>
                    <span>{{ $collectionEfficiency }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="h-3 rounded-full {{ $collectionEfficiency >= 90 ? 'bg-green-500' : ($collectionEfficiency >= 70 ? 'bg-yellow-500' : 'bg-red-500') }}" 
                         style="width: {{ min($collectionEfficiency, 100) }}%"></div>
                </div>
            </div>
            <div class="ml-4 text-right">
                <div class="text-2xl font-bold {{ $collectionEfficiency >= 90 ? 'text-green-600' : ($collectionEfficiency >= 70 ? 'text-yellow-600' : 'text-red-600') }}">
                    {{ $collectionEfficiency }}%
                </div>
            </div>
        </div>
    </div>

    <!-- Achievement Badges -->
    @if(count($achievementBadges) > 0)
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Achievement Badges</h3>
        <div class="flex flex-wrap gap-3">
            @foreach($achievementBadges as $badge)
                <div class="flex items-center px-3 py-2 bg-{{ $badge['color'] }}-50 border border-{{ $badge['color'] }}-200 rounded-lg">
                    <span class="text-lg mr-2">{{ $badge['icon'] }}</span>
                    <span class="text-sm font-medium text-{{ $badge['color'] }}-800">{{ $badge['name'] }}</span>
                </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Today's Collection Schedule -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Today's Collection Schedule</h3>
                <input type="date" wire:model.live="selectedDate" 
                       class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
            </div>
        </div>
        <div class="divide-y divide-gray-200">
            @forelse($todaySchedule as $schedule)
                <div class="p-4 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <div class="font-medium text-gray-900">{{ $schedule['member_name'] }}</div>
                                <span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">{{ $schedule['member_id'] }}</span>
                            </div>
                            <div class="text-sm text-gray-500 mt-1">
                                Installment #{{ $schedule['installment_no'] }} • ৳{{ number_format($schedule['amount'], 0) }}
                            </div>
                            <div class="text-xs text-gray-400 mt-1">
                                📞 {{ $schedule['phone'] }} • 📍 {{ Str::limit($schedule['address'], 30) }}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button wire:click="collectInstallment({{ $schedule['id'] }})" 
                                    class="px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700">
                                Collect
                            </button>
                            <button wire:click="markOverdue({{ $schedule['id'] }})" 
                                    class="px-3 py-1 bg-red-600 text-white text-xs rounded-md hover:bg-red-700">
                                Overdue
                            </button>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-8 text-center text-gray-500">
                    <svg class="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <p>No collections scheduled for this date</p>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Overdue Payments -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Overdue Payments</h3>
        </div>
        <div class="divide-y divide-gray-200">
            @forelse($overduePayments as $overdue)
                <div class="p-4 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <div class="font-medium text-gray-900">{{ $overdue['member_name'] }}</div>
                                <span class="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">{{ $overdue['member_id'] }}</span>
                                <span class="ml-2 px-2 py-1 text-xs 
                                    {{ $overdue['priority'] === 'high' ? 'bg-red-100 text-red-800' : 
                                       ($overdue['priority'] === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-orange-100 text-orange-800') }} 
                                    rounded-full">
                                    {{ $overdue['days_overdue'] }} days overdue
                                </span>
                            </div>
                            <div class="text-sm text-gray-500 mt-1">
                                Due: {{ $overdue['installment_date'] }} • ৳{{ number_format($overdue['amount'], 0) }}
                            </div>
                            <div class="text-xs text-gray-400 mt-1">
                                📞 {{ $overdue['phone'] }}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button wire:click="collectInstallment({{ $overdue['id'] }})" 
                                    class="px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700">
                                Collect Now
                            </button>
                            <a href="tel:{{ $overdue['phone'] }}" 
                               class="px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700">
                                Call
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-8 text-center text-gray-500">
                    <svg class="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p>No overdue payments! Great job!</p>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Monthly Performance Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Performance</h3>
        <div class="h-64">
            <canvas id="monthlyPerformanceChart"></canvas>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
        </div>
        <div class="divide-y divide-gray-200">
            @forelse($recentActivities as $activity)
                <div class="p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if($activity['type'] === 'collection')
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            @else
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div class="ml-4 flex-1">
                            <div class="text-sm text-gray-900">{{ $activity['description'] }}</div>
                            <div class="text-xs text-gray-500">{{ $activity['time'] }}</div>
                        </div>
                        @if($activity['amount'])
                            <div class="text-sm font-medium text-green-600">
                                ৳{{ number_format($activity['amount'], 0) }}
                            </div>
                        @endif
                    </div>
                </div>
            @empty
                <div class="p-8 text-center text-gray-500">
                    <p>No recent activities</p>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Quick Actions Panel -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <a href="{{ route('members.register') }}" 
           class="flex items-center justify-center p-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
            </svg>
            <span class="font-medium">Register Member</span>
        </a>
        
        <a href="{{ route('officer.collections') }}" 
           class="flex items-center justify-center p-6 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-150">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            <span class="font-medium">Collect Payment</span>
        </a>
        
        <a href="{{ route('officer.members') }}" 
           class="flex items-center justify-center p-6 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-150">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <span class="font-medium">View Members</span>
        </a>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('monthlyPerformanceChart').getContext('2d');
    const performanceData = @json($monthlyPerformance);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: performanceData.map(item => item.month),
            datasets: [{
                label: 'Collections (৳)',
                data: performanceData.map(item => item.collections),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true,
                yAxisID: 'y'
            }, {
                label: 'Members Added',
                data: performanceData.map(item => item.members),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: false,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Month'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Collections (৳)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '৳' + value.toLocaleString();
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Members'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
});
</script>
@endpush
