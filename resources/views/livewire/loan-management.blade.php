<div class="max-w-7xl mx-auto p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Loan Management</h2>
        <p class="text-gray-600">Manage active loans and payment schedules</p>
    </div>

    @if (session()->has('success'))
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {{ session('error') }}
        </div>
    @endif

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" wire:model.live="search" placeholder="Search member..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select wire:model.live="status_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Loans</option>
                    <option value="active">Active Loans</option>
                    <option value="completed">Completed Loans</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                <select wire:model.live="overdue_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Payments</option>
                    <option value="overdue">Overdue Payments</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Loans Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loan Details</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Payment</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($loans as $loan)
                        @php
                            $member = $loan->loanApplication->member;
                            $nextInstallment = $loan->installments()->where('status', 'pending')->orderBy('installment_date')->first();
                            $overdueCount = $loan->installments()->where('status', 'pending')->where('installment_date', '<', now()->toDateString())->count();
                            $paidCount = $loan->installments()->where('status', 'paid')->count();
                            $totalCount = $loan->installments()->count();
                            $progressPercentage = $totalCount > 0 ? ($paidCount / $totalCount) * 100 : 0;
                        @endphp
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $member->name }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ $member->member_id }}</div>
                                        <div class="text-sm text-gray-500">{{ $member->phone_number }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">৳{{ number_format($loan->loan_amount, 2) }}</div>
                                <div class="text-sm text-gray-500">{{ $loan->repayment_method }} - {{ $loan->installment_count }} installments</div>
                                <div class="text-sm text-gray-500">৳{{ number_format($loan->installment_amount, 2) }} each</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $paidCount }}/{{ $totalCount }} paid</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $progressPercentage }}%"></div>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">৳{{ number_format($loan->remaining_balance, 2) }} remaining</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($nextInstallment)
                                    <div class="text-sm text-gray-900">{{ $nextInstallment->installment_date->format('M d, Y') }}</div>
                                    <div class="text-sm text-gray-500">৳{{ number_format($nextInstallment->installment_amount, 2) }}</div>
                                    @if($nextInstallment->installment_date < now())
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Overdue
                                        </span>
                                    @endif
                                @else
                                    <span class="text-sm text-gray-500">Completed</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($loan->is_fully_paid)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Completed
                                    </span>
                                @elseif($overdueCount > 0)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        {{ $overdueCount }} Overdue
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        Active
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button wire:click="openLoanModal({{ $loan->id }})" 
                                            class="text-blue-600 hover:text-blue-900">
                                        View
                                    </button>
                                    @if(!$loan->is_fully_paid)
                                        <button wire:click="openEarlyPaymentModal({{ $loan->id }})" 
                                                class="text-green-600 hover:text-green-900">
                                            Early Pay
                                        </button>
                                        <div class="relative inline-block text-left">
                                            <button type="button" class="text-gray-600 hover:text-gray-900" onclick="toggleDropdown({{ $loan->id }})">
                                                ⋮
                                            </button>
                                            <div id="dropdown-{{ $loan->id }}" class="hidden absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
                                                <div class="py-1">
                                                    <button wire:click="openModificationModal({{ $loan->id }}, 'installment_change')" 
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        Modify Installment
                                                    </button>
                                                    <button wire:click="openModificationModal({{ $loan->id }}, 'extension')" 
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        Extend Duration
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <button wire:click="closeLoan({{ $loan->id }})" 
                                                class="text-gray-600 hover:text-gray-900">
                                            Close
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                No loans found
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-3 border-t border-gray-200">
            {{ $loans->links() }}
        </div>
    </div>

    <!-- Loan Details Modal -->
    @if($show_loan_modal && $selected_loan)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            Loan Details - {{ $selected_loan->loanApplication->member->name }}
                        </h3>
                        <button wire:click="closeLoanModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Loan Summary -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Loan Amount:</span>
                                <div class="font-medium">৳{{ number_format($selected_loan->loan_amount, 2) }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Total Repayment:</span>
                                <div class="font-medium">৳{{ number_format($selected_loan->total_repayment_amount, 2) }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Paid Amount:</span>
                                <div class="font-medium">৳{{ number_format($selected_loan->total_paid, 2) }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Remaining:</span>
                                <div class="font-medium">৳{{ number_format($selected_loan->remaining_balance, 2) }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Schedule -->
                    <div class="mb-4">
                        <h4 class="font-medium mb-2">Payment Schedule</h4>
                        <div class="max-h-96 overflow-y-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">No.</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Collected By</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($loan_installments as $installment)
                                        <tr class="{{ $installment->is_overdue ? 'bg-red-50' : '' }}">
                                            <td class="px-4 py-2 text-sm">{{ $installment->installment_no }}</td>
                                            <td class="px-4 py-2 text-sm">{{ $installment->installment_date->format('M d, Y') }}</td>
                                            <td class="px-4 py-2 text-sm">৳{{ number_format($installment->installment_amount, 2) }}</td>
                                            <td class="px-4 py-2 text-sm">
                                                @if($installment->status === 'paid')
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                        Paid
                                                    </span>
                                                @elseif($installment->is_overdue)
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                        Overdue
                                                    </span>
                                                @else
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                        Pending
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-4 py-2 text-sm">
                                                {{ $installment->collector->name ?? '-' }}
                                                @if($installment->collection_date)
                                                    <div class="text-xs text-gray-500">{{ $installment->collection_date->format('M d, Y') }}</div>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button wire:click="closeLoanModal"
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Early Payment Modal -->
    @if($show_early_payment_modal && $selected_loan)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            Early Payment - {{ $selected_loan->loanApplication->member->name }}
                        </h3>
                        <button wire:click="closeEarlyPaymentModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Amount (৳)</label>
                        <input type="number" wire:model.live="early_payment_amount" min="1" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @error('early_payment_amount') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Early Payment Discount (%)</label>
                        <input type="number" wire:model.live="early_payment_discount" min="0" max="20" step="0.1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    @if(!empty($early_payment_calculation))
                        <div class="bg-blue-50 p-4 rounded-lg mb-4">
                            <h4 class="font-medium mb-2">Payment Calculation</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">Remaining Balance:</span>
                                    <div class="font-medium">৳{{ number_format($early_payment_calculation['remaining_balance'], 2) }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">Discount Amount:</span>
                                    <div class="font-medium">৳{{ number_format($early_payment_calculation['discount_amount'], 2) }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">Final Payment:</span>
                                    <div class="font-medium">৳{{ number_format($early_payment_calculation['final_payment_amount'], 2) }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">Total Savings:</span>
                                    <div class="font-medium text-green-600">৳{{ number_format($early_payment_calculation['savings'], 2) }}</div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="flex justify-end space-x-4">
                        <button wire:click="closeEarlyPaymentModal"
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Cancel
                        </button>
                        <button wire:click="processEarlyPayment"
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                                wire:loading.attr="disabled">
                            <span wire:loading.remove>Process Payment</span>
                            <span wire:loading>Processing...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function toggleDropdown(loanId) {
    const dropdown = document.getElementById('dropdown-' + loanId);
    dropdown.classList.toggle('hidden');
    
    // Close other dropdowns
    document.querySelectorAll('[id^="dropdown-"]').forEach(el => {
        if (el.id !== 'dropdown-' + loanId) {
            el.classList.add('hidden');
        }
    });
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick^="toggleDropdown"]')) {
        document.querySelectorAll('[id^="dropdown-"]').forEach(el => {
            el.classList.add('hidden');
        });
    }
});
</script>
