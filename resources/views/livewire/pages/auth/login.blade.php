<?php

use App\Livewire\Forms\LoginForm;
use App\Models\Advertisement;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.guest')] class extends Component
{
    public LoginForm $form;
    public $advertisements;

    public function mount()
    {
        $this->advertisements = Advertisement::active()->get();
    }

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->form->authenticate();

        Session::regenerate();

        $redirectUrl = $this->form->getRedirectUrl();
        $this->redirect($redirectUrl, navigate: true);
    }
}; ?>

<div class="min-h-screen flex">
    <!-- Left Side - Login Form -->
    <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- <PERSON><PERSON> and <PERSON><PERSON> -->
            <div class="text-center">
                <div class="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center">
                    <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <h2 class="mt-6 text-3xl font-bold text-gray-900">{{ config('app.name') }}</h2>
                <p class="mt-2 text-sm text-gray-600">Microfinance Management System</p>
                <p class="text-sm text-gray-500">Sign in to your account</p>
            </div>

            <!-- Login Form -->
            <div class="bg-white py-8 px-6 shadow-xl rounded-lg">
                <!-- Session Status -->
                <x-auth-session-status class="mb-4" :status="session('status')" />

                <form wire:submit="login" class="space-y-6">
                    <!-- Email Address -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <div class="mt-1 relative">
                            <input wire:model="form.email" id="email" type="email" required autofocus
                                class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-150 ease-in-out"
                                placeholder="Enter your email">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                            </div>
                        </div>
                        <x-input-error :messages="$errors->get('form.email')" class="mt-2" />
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <div class="mt-1 relative">
                            <input wire:model="form.password" id="password" type="password" required
                                class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-150 ease-in-out"
                                placeholder="Enter your password">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                        </div>
                        <x-input-error :messages="$errors->get('form.password')" class="mt-2" />
                    </div>

                    <!-- Remember Me and Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input wire:model="form.remember" id="remember" type="checkbox"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="remember" class="ml-2 block text-sm text-gray-700">Remember me</label>
                        </div>

                        @if (Route::has('password.request'))
                            <a href="{{ route('password.request') }}" wire:navigate
                                class="text-sm text-blue-600 hover:text-blue-500 font-medium">
                                Forgot password?
                            </a>
                        @endif
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <svg class="h-5 w-5 text-blue-300 group-hover:text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                </svg>
                            </span>
                            Sign In
                        </button>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="text-center">
                <p class="text-xs text-gray-500">
                    © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
                </p>
            </div>
        </div>
    </div>

    <!-- Right Side - Advertisements -->
    <div class="hidden lg:flex lg:flex-1 lg:flex-col bg-gradient-to-br from-blue-600 via-blue-700 to-green-600">
        <div class="flex-1 flex flex-col justify-center px-8 py-12">
            <div class="text-center text-white mb-8">
                <h3 class="text-2xl font-bold mb-4">Welcome to {{ config('app.name') }}</h3>
                <p class="text-blue-100 text-lg">Empowering communities through microfinance</p>
            </div>

            @if($advertisements && $advertisements->count() > 0)
                <div class="space-y-6" x-data="{ currentAd: 0, ads: {{ $advertisements->count() }} }"
                     x-init="setInterval(() => { currentAd = (currentAd + 1) % ads }, 5000)">
                    @foreach($advertisements as $index => $ad)
                        <div x-show="currentAd === {{ $index }}"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform translate-y-4"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             class="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
                            @if($ad->image)
                                <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}"
                                     class="mx-auto h-32 w-auto object-contain mb-4 rounded">
                            @endif
                            <h4 class="text-xl font-semibold text-white mb-2">{{ $ad->title }}</h4>
                            @if($ad->link_url)
                                <a href="{{ $ad->link_url }}" target="_blank"
                                   class="inline-block mt-4 px-6 py-2 bg-white text-blue-600 rounded-full font-medium hover:bg-blue-50 transition duration-150">
                                    Learn More
                                </a>
                            @endif
                        </div>
                    @endforeach

                    <!-- Advertisement Indicators -->
                    @if($advertisements->count() > 1)
                        <div class="flex justify-center space-x-2 mt-6">
                            @foreach($advertisements as $index => $ad)
                                <button @click="currentAd = {{ $index }}"
                                        :class="currentAd === {{ $index }} ? 'bg-white' : 'bg-white/30'"
                                        class="w-3 h-3 rounded-full transition duration-150"></button>
                            @endforeach
                        </div>
                    @endif
                </div>
            @else
                <!-- Default content when no advertisements -->
                <div class="text-center text-white">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
                        <svg class="mx-auto h-16 w-16 text-white mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <h4 class="text-xl font-semibold mb-2">Fast & Secure</h4>
                        <p class="text-blue-100">Experience seamless microfinance management with our secure platform.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
