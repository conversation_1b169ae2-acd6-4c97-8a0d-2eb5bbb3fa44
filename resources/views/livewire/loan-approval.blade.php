<div class="max-w-7xl mx-auto p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Loan Application Management</h2>
        <p class="text-gray-600">Review and approve loan applications</p>
    </div>

    @if (session()->has('success'))
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {{ session('error') }}
        </div>
    @endif

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select wire:model.live="status_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" wire:model.live="search" placeholder="Search member..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" wire:model.live="date_from" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" wire:model.live="date_to" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div class="flex items-end">
                @if(count($selected_applications) > 0)
                    <button wire:click="bulkApprove" 
                            class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                        Bulk Approve ({{ count($selected_applications) }})
                    </button>
                @endif
            </div>
        </div>
    </div>

    <!-- Applications Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" wire:model.live="select_all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cycle</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($applications as $application)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($application->status === 'pending')
                                    <input type="checkbox" wire:model.live="selected_applications" value="{{ $application->id }}" 
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $application->member->name }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ $application->member->member_id }}</div>
                                        <div class="text-sm text-gray-500">{{ $application->member->phone_number }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">৳{{ number_format($application->applied_amount, 2) }}</div>
                                @if($application->advance_payment > 0)
                                    <div class="text-sm text-gray-500">Advance: ৳{{ number_format($application->advance_payment, 2) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $application->loan_cycle_number }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $application->applied_at->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($application->status === 'pending')
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                @elseif($application->status === 'approved')
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        Rejected
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                @if($application->status === 'pending')
                                    <button wire:click="openReviewModal({{ $application->id }}, 'approve')" 
                                            class="text-green-600 hover:text-green-900">
                                        Approve
                                    </button>
                                    <button wire:click="openReviewModal({{ $application->id }}, 'reject')" 
                                            class="text-red-600 hover:text-red-900">
                                        Reject
                                    </button>
                                @else
                                    <span class="text-gray-400">
                                        Reviewed by {{ $application->reviewer->name ?? 'System' }}
                                    </span>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No applications found
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-3 border-t border-gray-200">
            {{ $applications->links() }}
        </div>
    </div>

    <!-- Review Modal -->
    @if($show_review_modal && $reviewing_application)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            {{ ucfirst($review_action) }} Loan Application
                        </h3>
                        <button wire:click="closeReviewModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Application Details -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h4 class="font-medium mb-2">Application Details</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Member:</span>
                                <span class="font-medium">{{ $reviewing_application->member->name }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Applied Amount:</span>
                                <span class="font-medium">৳{{ number_format($reviewing_application->applied_amount, 2) }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Cycle:</span>
                                <span class="font-medium">{{ $reviewing_application->loan_cycle_number }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Advance Payment:</span>
                                <span class="font-medium">৳{{ number_format($reviewing_application->advance_payment, 2) }}</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="text-gray-600">Reason:</span>
                            <p class="text-sm">{{ $reviewing_application->reason }}</p>
                        </div>
                    </div>

                    @if($review_action === 'approve')
                        <!-- Loan Configuration -->
                        <div class="mb-4">
                            <h4 class="font-medium mb-2">Loan Configuration</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Loan Amount (৳)</label>
                                    <input type="number" wire:model.live="loan_amount" min="1000" step="100"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    @error('loan_amount') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Advance Payment (৳)</label>
                                    <input type="number" wire:model.live="advance_payment" min="0" step="100"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    @error('advance_payment') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Repayment Method</label>
                                    <select wire:model.live="repayment_method" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="monthly">Monthly</option>
                                        <option value="weekly">Weekly</option>
                                    </select>
                                    @error('repayment_method') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Duration (Months)</label>
                                    <select wire:model.live="duration_months" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        @for($i = 3; $i <= 60; $i += 3)
                                            <option value="{{ $i }}">{{ $i }} Months</option>
                                        @endfor
                                    </select>
                                    @error('duration_months') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Loan Calculation -->
                        @if(!empty($loan_calculation))
                            <div class="bg-blue-50 p-4 rounded-lg mb-4">
                                <h4 class="font-medium mb-2">Loan Calculation</h4>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <div class="text-gray-600">Total Interest</div>
                                        <div class="font-medium">৳{{ number_format($loan_calculation['total_interest'], 2) }}</div>
                                    </div>
                                    <div>
                                        <div class="text-gray-600">Total Repayment</div>
                                        <div class="font-medium">৳{{ number_format($loan_calculation['total_repayment_amount'], 2) }}</div>
                                    </div>
                                    <div>
                                        <div class="text-gray-600">Installments</div>
                                        <div class="font-medium">{{ $loan_calculation['installment_count'] }}</div>
                                    </div>
                                    <div>
                                        <div class="text-gray-600">{{ ucfirst($repayment_method) }} Amount</div>
                                        <div class="font-medium">৳{{ number_format($loan_calculation['installment_amount'], 2) }}</div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif

                    <!-- Review Comments -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ $review_action === 'approve' ? 'Approval' : 'Rejection' }} Comments
                        </label>
                        <textarea wire:model="review_comments" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Enter your comments..."></textarea>
                        @error('review_comments') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- Modal Actions -->
                    <div class="flex justify-end space-x-4">
                        <button wire:click="closeReviewModal" 
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Cancel
                        </button>
                        <button wire:click="submitReview" 
                                class="px-4 py-2 {{ $review_action === 'approve' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700' }} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                wire:loading.attr="disabled">
                            <span wire:loading.remove>{{ ucfirst($review_action) }}</span>
                            <span wire:loading>Processing...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
