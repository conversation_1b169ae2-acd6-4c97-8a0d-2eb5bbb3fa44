<div class="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Loan Application</h2>
        <p class="text-gray-600">Apply for a new loan with our easy application process</p>
    </div>

    @if (session()->has('success'))
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('success') }}
        </div>
    @endif

    <form wire:submit.prevent="submit" class="space-y-6">
        <!-- Member Selection -->
        @if(auth()->user()->role !== 'member')
        <div class="bg-gray-50 p-4 rounded-lg">
            <label class="block text-sm font-medium text-gray-700 mb-2">Select Member</label>
            
            @if(!$selected_member)
                <div class="relative">
                    <input type="text" 
                           wire:model.live="member_search" 
                           placeholder="Search by name, member ID, or phone..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    
                    @if(count($members) > 0)
                        <div class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                            @foreach($members as $member)
                                <div wire:click="selectMember({{ $member->id }})" 
                                     class="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b">
                                    <div class="font-medium">{{ $member->name }}</div>
                                    <div class="text-sm text-gray-600">ID: {{ $member->member_id }} | Phone: {{ $member->phone_number }}</div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @else
                <div class="flex items-center justify-between bg-blue-50 p-3 rounded-md">
                    <div>
                        <div class="font-medium">{{ $selected_member->name }}</div>
                        <div class="text-sm text-gray-600">ID: {{ $selected_member->member_id }} | Phone: {{ $selected_member->phone_number }}</div>
                    </div>
                    <button type="button" wire:click="$set('selected_member', null)" class="text-red-600 hover:text-red-800">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            @endif
            @error('member_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
        </div>
        @endif

        <!-- Loan Details -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Loan Amount (৳)</label>
                <input type="number" 
                       wire:model.live="applied_amount" 
                       min="1000" 
                       max="500000" 
                       step="100"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="Enter loan amount">
                @error('applied_amount') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Advance Payment (৳)</label>
                <input type="number" 
                       wire:model.live="advance_payment" 
                       min="0" 
                       step="100"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="Enter advance payment (optional)">
                @error('advance_payment') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Repayment Method</label>
                <select wire:model.live="repayment_method" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="monthly">Monthly</option>
                    <option value="weekly">Weekly</option>
                </select>
                @error('repayment_method') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Duration (Months)</label>
                <select wire:model.live="duration_months" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    @for($i = 3; $i <= 60; $i += 3)
                        <option value="{{ $i }}">{{ $i }} Months</option>
                    @endfor
                </select>
                @error('duration_months') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
        </div>

        <!-- Loan Calculator -->
        @if($show_calculator && !empty($loan_calculation))
        <div class="bg-blue-50 p-4 rounded-lg">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-medium text-gray-900">Loan Calculation</h3>
                <button type="button" wire:click="toggleCalculator" class="text-blue-600 hover:text-blue-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                    <div class="text-gray-600">Loan Amount</div>
                    <div class="font-medium">৳{{ number_format($loan_calculation['loan_amount'], 2) }}</div>
                </div>
                <div>
                    <div class="text-gray-600">Total Interest</div>
                    <div class="font-medium">৳{{ number_format($loan_calculation['total_interest'], 2) }}</div>
                </div>
                <div>
                    <div class="text-gray-600">Total Repayment</div>
                    <div class="font-medium">৳{{ number_format($loan_calculation['total_repayment_amount'], 2) }}</div>
                </div>
                <div>
                    <div class="text-gray-600">{{ ucfirst($repayment_method) }} Installment</div>
                    <div class="font-medium">৳{{ number_format($loan_calculation['installment_amount'], 2) }}</div>
                </div>
            </div>
        </div>
        @elseif($applied_amount >= 1000)
        <div class="text-center">
            <button type="button" wire:click="toggleCalculator" class="text-blue-600 hover:text-blue-800 font-medium">
                📊 Show Loan Calculator
            </button>
        </div>
        @endif

        <!-- Reason and Recommender -->
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Reason for Loan</label>
                <textarea wire:model="reason" 
                          rows="3" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Please provide a detailed reason for the loan..."></textarea>
                @error('reason') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Recommender Name</label>
                <input type="text" 
                       wire:model="recommender" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="Enter recommender's full name">
                @error('recommender') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
        </div>

        <!-- Document Upload -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Supporting Documents (Optional)</label>
            <input type="file" 
                   wire:model="documents" 
                   multiple 
                   accept=".pdf,.jpg,.jpeg,.png"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <p class="text-sm text-gray-500 mt-1">Upload PDF, JPG, JPEG, or PNG files (max 2MB each)</p>
            @error('documents.*') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            
            @if(count($documents) > 0)
                <div class="mt-2 space-y-2">
                    @foreach($documents as $index => $document)
                        <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
                            <span class="text-sm">{{ $document->getClientOriginalName() }}</span>
                            <button type="button" wire:click="removeDocument({{ $index }})" class="text-red-600 hover:text-red-800">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end space-x-4">
            <button type="button" 
                    onclick="window.history.back()" 
                    class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Cancel
            </button>
            <button type="submit" 
                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    wire:loading.attr="disabled">
                <span wire:loading.remove>Submit Application</span>
                <span wire:loading>Submitting...</span>
            </button>
        </div>
    </form>
</div>
