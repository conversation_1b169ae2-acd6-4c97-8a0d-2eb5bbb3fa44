<div class="max-w-7xl mx-auto p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Installment Tracking</h2>
        <p class="text-gray-600">Monitor payment schedules and send reminders</p>
    </div>

    @if (session()->has('success'))
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('info'))
        <div class="mb-6 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
            {{ session('info') }}
        </div>
    @endif

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Due Today</p>
                    <p class="text-2xl font-semibold text-blue-600">{{ count($due_today_installments) }}</p>
                    <p class="text-sm text-gray-500">৳{{ number_format($due_today_installments->sum('installment_amount'), 2) }}</p>
                </div>
                <button wire:click="sendBulkReminders('due_today')" 
                        class="px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                    Send Reminders
                </button>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Overdue</p>
                    <p class="text-2xl font-semibold text-red-600">{{ count($overdue_installments) }}</p>
                    <p class="text-sm text-gray-500">৳{{ number_format($overdue_installments->sum('installment_amount'), 2) }}</p>
                </div>
                <button wire:click="sendBulkReminders('overdue')" 
                        class="px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                    Send Reminders
                </button>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Collection Rate</p>
                    @php
                        $totalDue = $due_today_installments->count() + $overdue_installments->count();
                        $totalPaid = $due_today_installments->where('status', 'paid')->count();
                        $rate = $totalDue > 0 ? ($totalPaid / $totalDue) * 100 : 0;
                    @endphp
                    <p class="text-2xl font-semibold text-green-600">{{ number_format($rate, 1) }}%</p>
                    <p class="text-sm text-gray-500">This month</p>
                </div>
            </div>
        </div>
    </div>

    <!-- View Mode Selector -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex items-center justify-between">
            <div class="flex space-x-4">
                <button wire:click="$set('view_mode', 'calendar')" 
                        class="px-4 py-2 rounded-md {{ $view_mode === 'calendar' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700' }}">
                    📅 Calendar View
                </button>
                <button wire:click="$set('view_mode', 'list')" 
                        class="px-4 py-2 rounded-md {{ $view_mode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700' }}">
                    📋 List View
                </button>
            </div>
        </div>
    </div>

    @if($view_mode === 'calendar')
        <!-- Calendar View -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-medium text-gray-900">
                    {{ \Carbon\Carbon::create($calendar_year, $calendar_month, 1)->format('F Y') }}
                </h3>
                <div class="flex space-x-2">
                    <button wire:click="previousMonth" class="p-2 text-gray-600 hover:text-gray-900">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button wire:click="nextMonth" class="p-2 text-gray-600 hover:text-gray-900">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-7 gap-px bg-gray-200">
                <!-- Calendar Headers -->
                @foreach(['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as $day)
                    <div class="bg-gray-50 p-2 text-center text-sm font-medium text-gray-700">
                        {{ $day }}
                    </div>
                @endforeach

                <!-- Calendar Days -->
                @php
                    $startDate = \Carbon\Carbon::create($calendar_year, $calendar_month, 1);
                    $startOfWeek = $startDate->copy()->startOfWeek();
                    $endOfMonth = $startDate->copy()->endOfMonth();
                    $endOfWeek = $endOfMonth->copy()->endOfWeek();
                @endphp

                @for($date = $startOfWeek; $date <= $endOfWeek; $date->addDay())
                    @php
                        $dateString = $date->format('Y-m-d');
                        $dayData = $calendar_data[$dateString] ?? null;
                        $isCurrentMonth = $date->month === $calendar_month;
                        $isToday = $date->isToday();
                    @endphp
                    
                    <div class="bg-white p-2 h-24 {{ !$isCurrentMonth ? 'text-gray-400' : '' }} {{ $isToday ? 'bg-blue-50' : '' }} cursor-pointer hover:bg-gray-50"
                         wire:click="selectDate('{{ $dateString }}')">
                        <div class="text-sm font-medium">{{ $date->day }}</div>
                        
                        @if($dayData && $isCurrentMonth)
                            <div class="mt-1 space-y-1">
                                @if($dayData['pending_count'] > 0)
                                    <div class="text-xs bg-yellow-100 text-yellow-800 px-1 rounded">
                                        {{ $dayData['pending_count'] }} pending
                                    </div>
                                @endif
                                @if($dayData['overdue_count'] > 0)
                                    <div class="text-xs bg-red-100 text-red-800 px-1 rounded">
                                        {{ $dayData['overdue_count'] }} overdue
                                    </div>
                                @endif
                                @if($dayData['paid_count'] > 0)
                                    <div class="text-xs bg-green-100 text-green-800 px-1 rounded">
                                        {{ $dayData['paid_count'] }} paid
                                    </div>
                                @endif
                                @if($dayData['total_amount'] > 0)
                                    <div class="text-xs text-gray-600">
                                        ৳{{ number_format($dayData['total_amount'], 0) }}
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                @endfor
            </div>
        </div>

    @elseif($view_mode === 'list')
        <!-- List View -->
        <div class="bg-white p-4 rounded-lg shadow mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input type="date" wire:model.live="selected_date" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select wire:model.live="status_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" wire:model.live="search" placeholder="Search member..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
        </div>

        <!-- Installments Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($installments as $installment)
                            @php
                                $member = $installment->loan->loanApplication->member;
                                $isOverdue = $installment->is_overdue;
                            @endphp
                            <tr class="hover:bg-gray-50 {{ $isOverdue ? 'bg-red-50' : '' }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $member->name }}</div>
                                            <div class="text-sm text-gray-500">ID: {{ $member->member_id }}</div>
                                            <div class="text-sm text-gray-500">{{ $member->phone_number }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">Loan #{{ $installment->loan->id }}</div>
                                    <div class="text-sm text-gray-500">Installment #{{ $installment->installment_no }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">৳{{ number_format($installment->installment_amount, 2) }}</div>
                                    @if($installment->due > 0)
                                        <div class="text-sm text-red-500">Due: ৳{{ number_format($installment->due, 2) }}</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $installment->installment_date->format('M d, Y') }}
                                    @if($isOverdue)
                                        <div class="text-xs text-red-500">
                                            {{ $installment->installment_date->diffForHumans() }}
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($installment->status === 'pending')
                                        @if($isOverdue)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                Overdue
                                            </span>
                                        @else
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Pending
                                            </span>
                                        @endif
                                    @elseif($installment->status === 'paid')
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Paid
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <button wire:click="openMemberModal({{ $member->id }})" 
                                            class="text-blue-600 hover:text-blue-900">
                                        View History
                                    </button>
                                    @if($installment->status === 'pending')
                                        <button wire:click="openReminderModal([{{ $installment->id }}])" 
                                                class="text-green-600 hover:text-green-900">
                                            Send Reminder
                                        </button>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    No installments found for the selected date
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($installments))
                <div class="px-6 py-3 border-t border-gray-200">
                    {{ $installments->links() }}
                </div>
            @endif
        </div>
    @endif

    <!-- Member History Modal -->
    @if($show_member_modal && $selected_member)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            Payment History - {{ $selected_member->name }}
                        </h3>
                        <button wire:click="closeMemberModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Member Info -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Member ID:</span>
                                <div class="font-medium">{{ $selected_member->member_id }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Phone:</span>
                                <div class="font-medium">{{ $selected_member->phone_number }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Total Loans:</span>
                                <div class="font-medium">{{ $selected_member->loanApplications->where('status', 'approved')->count() }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Active Loans:</span>
                                <div class="font-medium">{{ $selected_member->activeLoans()->count() }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment History -->
                    <div class="max-h-96 overflow-y-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Loan</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Installment</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Due Date</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Collected</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($member_installments as $installment)
                                    <tr class="{{ $installment->is_overdue ? 'bg-red-50' : '' }}">
                                        <td class="px-4 py-2 text-sm">#{{ $installment->loan->id }}</td>
                                        <td class="px-4 py-2 text-sm">#{{ $installment->installment_no }}</td>
                                        <td class="px-4 py-2 text-sm">{{ $installment->installment_date->format('M d, Y') }}</td>
                                        <td class="px-4 py-2 text-sm">৳{{ number_format($installment->installment_amount, 2) }}</td>
                                        <td class="px-4 py-2 text-sm">
                                            @if($installment->status === 'paid')
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                    Paid
                                                </span>
                                            @elseif($installment->is_overdue)
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                    Overdue
                                                </span>
                                            @else
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    Pending
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-2 text-sm">
                                            {{ $installment->collector->name ?? '-' }}
                                            @if($installment->collection_date)
                                                <div class="text-xs text-gray-500">{{ $installment->collection_date->format('M d, Y') }}</div>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="flex justify-end mt-4">
                        <button wire:click="closeMemberModal"
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Reminder Modal -->
    @if($show_reminder_modal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            Send Payment Reminder
                        </h3>
                        <button wire:click="closeReminderModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="mb-4">
                        <p class="text-sm text-gray-600">
                            Sending reminder to {{ count($selected_installments_for_reminder) }} member(s)
                        </p>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reminder Type</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" wire:model="reminder_type" value="sms" class="form-radio">
                                <span class="ml-2 text-sm">SMS Only</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" wire:model="reminder_type" value="email" class="form-radio">
                                <span class="ml-2 text-sm">Email Only</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" wire:model="reminder_type" value="both" class="form-radio">
                                <span class="ml-2 text-sm">Both SMS & Email</span>
                            </label>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea wire:model="reminder_message" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Enter reminder message..."></textarea>
                        <div class="text-sm text-gray-500 mt-1">
                            Character count: {{ strlen($reminder_message) }}/160
                        </div>
                        @error('reminder_message') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button wire:click="closeReminderModal"
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Cancel
                        </button>
                        <button wire:click="sendReminders"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                wire:loading.attr="disabled">
                            <span wire:loading.remove">Send Reminders</span>
                            <span wire:loading>Sending...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
