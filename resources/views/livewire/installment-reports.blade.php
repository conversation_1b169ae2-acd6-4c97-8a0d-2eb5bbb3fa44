<div class="max-w-7xl mx-auto p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Installment Reports</h2>
        <p class="text-gray-600">Comprehensive reporting and analytics for installment collections</p>
    </div>

    @if (session()->has('success'))
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('success') }}
        </div>
    @endif

    <!-- Report Filters -->
    <div class="bg-white p-6 rounded-lg shadow mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Report Configuration</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Report Type -->
            <div class="md:col-span-3">
                <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-2">
                    <button wire:click="$set('report_type', 'daily_collection')" 
                            class="px-3 py-2 text-sm rounded-md {{ $report_type === 'daily_collection' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700' }}">
                        Daily Collection
                    </button>
                    <button wire:click="$set('report_type', 'overdue_analysis')" 
                            class="px-3 py-2 text-sm rounded-md {{ $report_type === 'overdue_analysis' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700' }}">
                        Overdue Analysis
                    </button>
                    <button wire:click="$set('report_type', 'collection_efficiency')" 
                            class="px-3 py-2 text-sm rounded-md {{ $report_type === 'collection_efficiency' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700' }}">
                        Collection Efficiency
                    </button>
                    <button wire:click="$set('report_type', 'officer_performance')" 
                            class="px-3 py-2 text-sm rounded-md {{ $report_type === 'officer_performance' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700' }}">
                        Officer Performance
                    </button>
                    <button wire:click="$set('report_type', 'branch_summary')" 
                            class="px-3 py-2 text-sm rounded-md {{ $report_type === 'branch_summary' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700' }}">
                        Branch Summary
                    </button>
                </div>
            </div>

            <!-- Date Range -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                <input type="date" wire:model.live="date_from" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                <input type="date" wire:model.live="date_to" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <!-- Branch Filter -->
            @if(count($branches) > 1)
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Branch</label>
                <select wire:model.live="branch_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Branches</option>
                    @foreach($branches as $branch)
                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                    @endforeach
                </select>
            </div>
            @endif

            <!-- Officer Filter -->
            @if($report_type === 'officer_performance' && count($officers) > 0)
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Field Officer</label>
                <select wire:model.live="officer_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Officers</option>
                    @foreach($officers as $officer)
                        <option value="{{ $officer->id }}">{{ $officer->name }}</option>
                    @endforeach
                </select>
            </div>
            @endif
        </div>

        <!-- Export Options -->
        <div class="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
            <div class="flex items-center space-x-4">
                <label class="block text-sm font-medium text-gray-700">Export Format:</label>
                <select wire:model="export_format" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="excel">Excel</option>
                    <option value="pdf">PDF</option>
                    <option value="csv">CSV</option>
                </select>
            </div>
            <button wire:click="exportReport" 
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                📥 Export Report
            </button>
        </div>
    </div>

    <!-- Summary Statistics -->
    @if(!empty($summary_stats))
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        @if($report_type === 'daily_collection')
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Amount</div>
                <div class="text-2xl font-semibold text-blue-600">৳{{ number_format($summary_stats['total_amount'], 2) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Collections</div>
                <div class="text-2xl font-semibold text-green-600">{{ number_format($summary_stats['total_collections']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Unique Members</div>
                <div class="text-2xl font-semibold text-purple-600">{{ number_format($summary_stats['unique_members']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Average Daily</div>
                <div class="text-2xl font-semibold text-orange-600">৳{{ number_format($summary_stats['average_daily'], 2) }}</div>
            </div>
        @elseif($report_type === 'overdue_analysis')
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Overdue Amount</div>
                <div class="text-2xl font-semibold text-red-600">৳{{ number_format($summary_stats['total_overdue_amount'], 2) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Overdue Count</div>
                <div class="text-2xl font-semibold text-red-600">{{ number_format($summary_stats['total_overdue_count']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Affected Members</div>
                <div class="text-2xl font-semibold text-orange-600">{{ number_format($summary_stats['unique_members']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Average Amount</div>
                <div class="text-2xl font-semibold text-blue-600">৳{{ number_format($summary_stats['average_overdue_amount'], 2) }}</div>
            </div>
        @elseif($report_type === 'collection_efficiency')
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Overall Efficiency</div>
                <div class="text-2xl font-semibold text-green-600">{{ number_format($summary_stats['overall_efficiency'], 1) }}%</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Due</div>
                <div class="text-2xl font-semibold text-blue-600">৳{{ number_format($summary_stats['total_due_amount'], 2) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Collected</div>
                <div class="text-2xl font-semibold text-green-600">৳{{ number_format($summary_stats['total_collected_amount'], 2) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Avg Daily Efficiency</div>
                <div class="text-2xl font-semibold text-purple-600">{{ number_format($summary_stats['average_daily_efficiency'], 1) }}%</div>
            </div>
        @elseif($report_type === 'officer_performance')
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Officers</div>
                <div class="text-2xl font-semibold text-blue-600">{{ number_format($summary_stats['total_officers']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Amount</div>
                <div class="text-2xl font-semibold text-green-600">৳{{ number_format($summary_stats['total_amount'], 2) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Collections</div>
                <div class="text-2xl font-semibold text-purple-600">{{ number_format($summary_stats['total_collections']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Top Performer</div>
                <div class="text-lg font-semibold text-orange-600">{{ $summary_stats['top_performer'] }}</div>
            </div>
        @elseif($report_type === 'branch_summary')
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Branches</div>
                <div class="text-2xl font-semibold text-blue-600">{{ number_format($summary_stats['total_branches']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Amount</div>
                <div class="text-2xl font-semibold text-green-600">৳{{ number_format($summary_stats['total_amount'], 2) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Total Collections</div>
                <div class="text-2xl font-semibold text-purple-600">{{ number_format($summary_stats['total_collections']) }}</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-sm font-medium text-gray-600">Top Branch</div>
                <div class="text-lg font-semibold text-orange-600">{{ $summary_stats['top_branch'] }}</div>
            </div>
        @endif
    </div>
    @endif

    <!-- Report Data -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
                @if($report_type === 'daily_collection') Daily Collection Report
                @elseif($report_type === 'overdue_analysis') Overdue Analysis Report
                @elseif($report_type === 'collection_efficiency') Collection Efficiency Report
                @elseif($report_type === 'officer_performance') Officer Performance Report
                @elseif($report_type === 'branch_summary') Branch Summary Report
                @endif
            </h3>
        </div>

        <div class="overflow-x-auto">
            @if($report_type === 'daily_collection')
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collections</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($report_data as $day)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ \Carbon\Carbon::parse($day['date'])->format('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ৳{{ number_format($day['total_amount'], 2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($day['total_count']) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($day['unique_members']) }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                    No data found for the selected period
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

            @elseif($report_type === 'overdue_analysis')
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Overdue Range</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($report_data as $range)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ $range['range'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($range['count']) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ৳{{ number_format($range['total_amount'], 2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format(($range['total_amount'] / $summary_stats['total_overdue_amount']) * 100, 1) }}%
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                    No overdue installments found
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

            @elseif($report_type === 'collection_efficiency')
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collected</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($report_data as $day)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ \Carbon\Carbon::parse($day['date'])->format('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $day['due_count'] }} (৳{{ number_format($day['due_amount'], 2) }})
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $day['collected_count'] }} (৳{{ number_format($day['collected_amount'], 2) }})
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: {{ min(100, $day['efficiency_percentage']) }}%"></div>
                                        </div>
                                        <span class="{{ $day['efficiency_percentage'] >= 80 ? 'text-green-600' : ($day['efficiency_percentage'] >= 60 ? 'text-yellow-600' : 'text-red-600') }}">
                                            {{ number_format($day['efficiency_percentage'], 1) }}%
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                    No data found for the selected period
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

            @elseif($report_type === 'officer_performance')
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Officer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collections</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg/Collection</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($report_data as $officer)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ $officer['officer_name'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ৳{{ number_format($officer['total_amount'], 2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($officer['total_collections']) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($officer['unique_members']) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ৳{{ number_format($officer['average_per_collection'], 2) }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                    No officer performance data found
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

            @elseif($report_type === 'branch_summary')
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collections</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Officers</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($report_data as $branch)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ $branch['branch_name'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ৳{{ number_format($branch['total_amount'], 2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($branch['total_collections']) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($branch['unique_members']) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($branch['unique_officers']) }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                    No branch summary data found
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            @endif
        </div>
    </div>
</div>
