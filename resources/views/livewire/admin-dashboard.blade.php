<div>
    <!-- Header Controls -->
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h3 class="text-lg font-medium text-gray-900">System Overview</h3>
            <p class="text-sm text-gray-500">Comprehensive analytics and system monitoring</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
            <select wire:model.live="selectedPeriod" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
            </select>
            <select wire:model.live="selectedBranch" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="">All Branches</option>
                @foreach($branches as $branch)
                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                @endforeach
            </select>
            <button wire:click="exportReport" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-150">
                Export Report
            </button>
        </div>
    </div>

    <!-- System Overview KPIs -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($systemOverview['total_users']) }}</dd>
                            <dd class="text-xs text-green-600">{{ $systemOverview['active_users'] }} active</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Branches</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($systemOverview['total_branches']) }}</dd>
                            <dd class="text-xs text-blue-600">{{ number_format($systemOverview['total_members']) }} members</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Loans</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($systemOverview['active_loans']) }}</dd>
                            <dd class="text-xs text-orange-600">৳{{ number_format($systemOverview['total_loan_amount'], 0) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Today's Collections</dt>
                            <dd class="text-lg font-medium text-gray-900">৳{{ number_format($systemOverview['total_collections_today'], 0) }}</dd>
                            <dd class="text-xs text-red-600">{{ $systemOverview['pending_applications'] }} pending</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary & Critical Alerts -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Financial Summary -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Financial Summary</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">৳{{ number_format($financialSummary['total_disbursed'], 0) }}</div>
                        <div class="text-sm text-gray-600">Total Disbursed</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">৳{{ number_format($financialSummary['total_collected'], 0) }}</div>
                        <div class="text-sm text-gray-600">Total Collected</div>
                    </div>
                    <div class="text-center p-4 bg-orange-50 rounded-lg">
                        <div class="text-2xl font-bold text-orange-600">৳{{ number_format($financialSummary['outstanding_amount'], 0) }}</div>
                        <div class="text-sm text-gray-600">Outstanding</div>
                    </div>
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">{{ $financialSummary['collection_efficiency'] }}%</div>
                        <div class="text-sm text-gray-600">Collection Rate</div>
                    </div>
                    <div class="text-center p-4 bg-red-50 rounded-lg">
                        <div class="text-2xl font-bold text-red-600">{{ $financialSummary['default_rate'] }}%</div>
                        <div class="text-sm text-gray-600">Default Rate</div>
                    </div>
                    <div class="text-center p-4 bg-indigo-50 rounded-lg">
                        <div class="text-2xl font-bold text-indigo-600">{{ $financialSummary['profit_margin'] }}%</div>
                        <div class="text-sm text-gray-600">Profit Margin</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Alerts -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Critical Alerts</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">Overdue Installments</span>
                        </div>
                        <span class="text-sm font-medium text-red-600">{{ $criticalAlerts['overdue_installments'] }}</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">Pending Approvals</span>
                        </div>
                        <span class="text-sm font-medium text-yellow-600">{{ $criticalAlerts['pending_approvals'] }}</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">Inactive Users</span>
                        </div>
                        <span class="text-sm font-medium text-orange-600">{{ $criticalAlerts['inactive_users'] }}</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-700">System Health</span>
                        </div>
                        <span class="text-sm font-medium text-green-600">{{ $criticalAlerts['system_health'] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Installment Trends Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Collection Trends</h3>
                <div class="h-64">
                    <canvas id="installmentTrendsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Default Rates by Branch -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Default Rates by Branch</h3>
                <div class="h-64">
                    <canvas id="defaultRatesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Performance Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Branch Performance Comparison</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manager</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Officers</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loans</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collections</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Overdue</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($branchPerformance as $branch)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $branch['name'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $branch['manager'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ number_format($branch['active_members']) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $branch['field_officers'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">৳{{ number_format($branch['total_loans'], 0) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">৳{{ number_format($branch['total_collections'], 0) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">৳{{ number_format($branch['overdue_amount'], 0) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        {{ $branch['collection_rate'] >= 90 ? 'bg-green-100 text-green-800' : 
                                           ($branch['collection_rate'] >= 70 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                        {{ $branch['collection_rate'] }}%
                                    </span>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">No branch data available</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Growth Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold {{ $growthMetrics['member_growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                {{ $growthMetrics['member_growth'] > 0 ? '+' : '' }}{{ $growthMetrics['member_growth'] }}%
            </div>
            <div class="text-sm text-gray-600">Member Growth</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold {{ $growthMetrics['loan_growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                {{ $growthMetrics['loan_growth'] > 0 ? '+' : '' }}{{ $growthMetrics['loan_growth'] }}%
            </div>
            <div class="text-sm text-gray-600">Loan Growth</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold {{ $growthMetrics['collection_growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                {{ $growthMetrics['collection_growth'] > 0 ? '+' : '' }}{{ $growthMetrics['collection_growth'] }}%
            </div>
            <div class="text-sm text-gray-600">Collection Growth</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ $growthMetrics['branch_expansion'] }}</div>
            <div class="text-sm text-gray-600">New Branches</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{{ route('admin.users') }}" class="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-150">
                        <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span class="text-blue-700 font-medium">Manage Users</span>
                    </a>
                    <a href="{{ route('admin.branches') }}" class="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition duration-150">
                        <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <span class="text-green-700 font-medium">Manage Branches</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">User Activity</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Recent Logins (7d)</span>
                        <span class="text-sm font-medium text-gray-900">{{ $userActivity['recent_logins'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Active Officers</span>
                        <span class="text-sm font-medium text-gray-900">{{ $userActivity['active_field_officers'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Active Managers</span>
                        <span class="text-sm font-medium text-gray-900">{{ $userActivity['active_managers'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">New Members Today</span>
                        <span class="text-sm font-medium text-green-600">{{ $userActivity['new_registrations_today'] }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">System Status</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Database: Online</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">API: Operational</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-400 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Backup: Scheduled</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Security: Active</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Installment Trends Chart
    const trendsCtx = document.getElementById('installmentTrendsChart').getContext('2d');
    const trendsData = @json($installmentTrends);
    
    new Chart(trendsCtx, {
        type: 'line',
        data: {
            labels: trendsData.map(item => item.date),
            datasets: [{
                label: 'Collections (৳)',
                data: trendsData.map(item => item.amount),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '৳' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Default Rates Chart
    const defaultCtx = document.getElementById('defaultRatesChart').getContext('2d');
    const defaultData = @json($defaultRates);
    
    new Chart(defaultCtx, {
        type: 'bar',
        data: {
            labels: defaultData.map(item => item.branch),
            datasets: [{
                label: 'Default Rate (%)',
                data: defaultData.map(item => item.rate),
                backgroundColor: defaultData.map(item => 
                    item.rate > 10 ? 'rgba(239, 68, 68, 0.8)' : 
                    item.rate > 5 ? 'rgba(245, 158, 11, 0.8)' : 
                    'rgba(34, 197, 94, 0.8)'
                ),
                borderColor: defaultData.map(item => 
                    item.rate > 10 ? 'rgb(239, 68, 68)' : 
                    item.rate > 5 ? 'rgb(245, 158, 11)' : 
                    'rgb(34, 197, 94)'
                ),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: Math.max(...defaultData.map(item => item.rate)) + 5,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>
@endpush
