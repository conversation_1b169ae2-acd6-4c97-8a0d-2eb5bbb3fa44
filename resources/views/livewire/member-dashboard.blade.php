<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                @if($member->photo)
                    <img src="{{ $member->photo_url }}" alt="{{ $member->name }}" 
                        class="h-16 w-16 rounded-full object-cover mr-4">
                @else
                    <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center mr-4">
                        <span class="text-xl font-medium text-gray-700">
                            {{ strtoupper(substr($member->name, 0, 2)) }}
                        </span>
                    </div>
                @endif
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Welcome, {{ $member->name }}</h1>
                    <p class="text-gray-600">Member ID: {{ $member->member_id }} | {{ $member->branch?->name ?? 'No Branch' }}</p>
                </div>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-500">Member Since</div>
                <div class="text-lg font-semibold text-gray-900">{{ $member->created_at->format('F Y') }}</div>
            </div>
        </div>
    </div>

    <!-- Account Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Savings -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Savings</dt>
                            <dd class="text-lg font-medium text-gray-900">৳{{ number_format($accountOverview['total_savings'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Loans -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Loans</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $accountOverview['active_loans_count'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Balance -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Outstanding Balance</dt>
                            <dd class="text-lg font-medium text-gray-900">৳{{ number_format($accountOverview['outstanding_balance'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Status -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 {{ $accountOverview['has_overdue'] ? 'bg-red-500' : 'bg-green-500' }} rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($accountOverview['has_overdue'])
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                @endif
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Account Status</dt>
                            <dd class="text-lg font-medium {{ $accountOverview['has_overdue'] ? 'text-red-600' : 'text-green-600' }}">
                                {{ $accountOverview['has_overdue'] ? 'Overdue' : 'Good Standing' }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button wire:click="setTab('overview')" 
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $selectedTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    Overview
                </button>
                <button wire:click="setTab('loans')" 
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $selectedTab === 'loans' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    Loans & Applications
                </button>
                <button wire:click="setTab('savings')" 
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $selectedTab === 'savings' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    Savings Accounts
                </button>
                <button wire:click="setTab('transactions')" 
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $selectedTab === 'transactions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    Transaction History
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="space-y-6">
        @if($selectedTab === 'overview')
            <!-- Overview Tab -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Active Loans -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Active Loans</h3>
                        @forelse($activeLoans as $loan)
                            <div class="border-b border-gray-200 pb-4 mb-4 last:border-b-0 last:pb-0 last:mb-0">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            Loan Amount: ৳{{ number_format($loan->loan_amount, 2) }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            Disbursed: {{ $loan->loan_date->format('M d, Y') }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $loan->repayment_method }} payments of ৳{{ number_format($loan->installment_amount, 2) }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900">
                                            ৳{{ number_format($loan->remaining_balance, 2) }}
                                        </div>
                                        <div class="text-xs text-gray-500">remaining</div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No active loans</p>
                        @endforelse
                    </div>
                </div>

                <!-- Upcoming Installments -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Upcoming Payments</h3>
                        @forelse($upcomingInstallments as $installment)
                            <div class="border-b border-gray-200 pb-3 mb-3 last:border-b-0 last:pb-0 last:mb-0">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            ৳{{ number_format($installment->installment_amount, 2) }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            Installment #{{ $installment->installment_no }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-900">
                                            {{ $installment->installment_date->format('M d, Y') }}
                                        </div>
                                        <div class="text-xs {{ $installment->installment_date->isPast() ? 'text-red-500' : 'text-gray-500' }}">
                                            {{ $installment->installment_date->diffForHumans() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No upcoming payments</p>
                        @endforelse
                    </div>
                </div>

                <!-- Overdue Installments -->
                @if($overdueInstallments->count() > 0)
                    <div class="lg:col-span-2">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <svg class="w-6 h-6 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <h3 class="text-lg font-medium text-red-800">Overdue Payments</h3>
                            </div>
                            <div class="space-y-3">
                                @foreach($overdueInstallments as $installment)
                                    <div class="flex justify-between items-center bg-white p-3 rounded border border-red-200">
                                        <div>
                                            <div class="text-sm font-medium text-red-900">
                                                ৳{{ number_format($installment->installment_amount, 2) }}
                                            </div>
                                            <div class="text-xs text-red-700">
                                                Due: {{ $installment->installment_date->format('M d, Y') }}
                                            </div>
                                        </div>
                                        <div class="text-xs text-red-600 font-medium">
                                            {{ $installment->installment_date->diffForHumans() }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        @if($selectedTab === 'loans')
            <!-- Loans Tab -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Loan Applications</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reviewed By</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loan Details</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($loanApplications ?? [] as $application)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $application->applied_at->format('M d, Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ৳{{ number_format($application->applied_amount, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @if($application->status === 'approved') bg-green-100 text-green-800
                                                @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                                @else bg-yellow-100 text-yellow-800 @endif">
                                                {{ ucfirst($application->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $application->reviewer?->name ?? 'Pending' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            @if($application->loan)
                                                <div class="text-xs">
                                                    <div>Disbursed: {{ $application->loan->loan_date->format('M d, Y') }}</div>
                                                    <div>Amount: ৳{{ number_format($application->loan->loan_amount, 2) }}</div>
                                                </div>
                                            @else
                                                -
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                            No loan applications found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if(isset($loanApplications) && $loanApplications->hasPages())
                        <div class="mt-4">
                            {{ $loanApplications->links() }}
                        </div>
                    @endif
                </div>
            </div>
        @endif

        @if($selectedTab === 'savings')
            <!-- Savings Tab -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Savings Accounts</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @forelse($savingsAccounts as $account)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">{{ $account->saving_type_label }}</h4>
                                        <p class="text-xs text-gray-500">{{ $account->account_number }}</p>
                                    </div>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </div>
                                
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Monthly Amount:</span>
                                        <span class="text-gray-900">৳{{ number_format($account->monthly_amount, 2) }}</span>
                                    </div>
                                    @if($account->fdr_amount)
                                        <div class="flex justify-between">
                                            <span class="text-gray-500">FDR Amount:</span>
                                            <span class="text-gray-900">৳{{ number_format($account->fdr_amount, 2) }}</span>
                                        </div>
                                    @endif
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Method:</span>
                                        <span class="text-gray-900">{{ $account->saving_method_label }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">Started:</span>
                                        <span class="text-gray-900">{{ $account->start_date->format('M d, Y') }}</span>
                                    </div>
                                    @if($account->nominee_name)
                                        <div class="flex justify-between">
                                            <span class="text-gray-500">Nominee:</span>
                                            <span class="text-gray-900">{{ $account->nominee_name }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="col-span-full text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No savings accounts</h3>
                                <p class="mt-1 text-sm text-gray-500">Contact your field officer to open a savings account.</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        @endif

        @if($selectedTab === 'transactions')
            <!-- Transactions Tab -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Transaction History</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collected By</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($recentTransactions ?? [] as $transaction)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $transaction->collection_date->format('M d, Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            Loan Payment
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ৳{{ number_format($transaction->installment_amount, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $transaction->collector?->name ?? 'System' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            Installment #{{ $transaction->installment_no }}
                                            @if($transaction->due > 0)
                                                <span class="text-red-600">(Due: ৳{{ number_format($transaction->due, 2) }})</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                            No transactions found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if(isset($recentTransactions) && $recentTransactions->hasPages())
                        <div class="mt-4">
                            {{ $recentTransactions->links() }}
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>
