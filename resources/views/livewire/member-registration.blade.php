<div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Member Registration</h1>
        <p class="text-gray-600">Register a new member in the microfinance system</p>
    </div>

    <!-- Progress Bar -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            @for($i = 1; $i <= $totalSteps; $i++)
                <div class="flex items-center {{ $i < $totalSteps ? 'flex-1' : '' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 
                        {{ $currentStep >= $i ? 'bg-blue-600 border-blue-600 text-white' : 'border-gray-300 text-gray-500' }}">
                        @if($currentStep > $i)
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        @else
                            {{ $i }}
                        @endif
                    </div>
                    @if($i < $totalSteps)
                        <div class="flex-1 h-1 mx-4 {{ $currentStep > $i ? 'bg-blue-600' : 'bg-gray-300' }}"></div>
                    @endif
                </div>
            @endfor
        </div>
        
        <div class="flex justify-between text-sm text-gray-600">
            <span class="{{ $currentStep >= 1 ? 'text-blue-600 font-medium' : '' }}">Basic Info</span>
            <span class="{{ $currentStep >= 2 ? 'text-blue-600 font-medium' : '' }}">Address & ID</span>
            <span class="{{ $currentStep >= 3 ? 'text-blue-600 font-medium' : '' }}">Photo & Reference</span>
            <span class="{{ $currentStep >= 4 ? 'text-blue-600 font-medium' : '' }}">Branch Assignment</span>
        </div>
    </div>

    <!-- Auto-save indicator -->
    @if(session()->has('member_registration_autosave'))
        <div class="mb-4 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm">Form data has been auto-saved. You can continue from where you left off.</span>
            </div>
        </div>
    @endif

    <form wire:submit="submit">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <!-- Step 1: Basic Information -->
            @if($currentStep === 1)
                <div class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Full Name *</label>
                            <input wire:model.blur="name" type="text" id="name" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Enter full name">
                            @error('name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Father/Husband Name -->
                        <div>
                            <label for="father_or_husband_name" class="block text-sm font-medium text-gray-700">Father/Husband Name *</label>
                            <input wire:model.blur="father_or_husband_name" type="text" id="father_or_husband_name" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Enter father or husband name">
                            @error('father_or_husband_name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Mother Name -->
                        <div>
                            <label for="mother_name" class="block text-sm font-medium text-gray-700">Mother's Name *</label>
                            <input wire:model.blur="mother_name" type="text" id="mother_name" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Enter mother's name">
                            @error('mother_name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Date of Birth -->
                        <div>
                            <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth *</label>
                            <input wire:model.blur="date_of_birth" type="date" id="date_of_birth" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            @error('date_of_birth') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Religion -->
                        <div>
                            <label for="religion" class="block text-sm font-medium text-gray-700">Religion *</label>
                            <select wire:model.blur="religion" id="religion" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Religion</option>
                                @foreach($religions as $religionOption)
                                    <option value="{{ $religionOption }}">{{ $religionOption }}</option>
                                @endforeach
                            </select>
                            @error('religion') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number *</label>
                            <input wire:model.blur="phone_number" type="tel" id="phone_number" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="01XXXXXXXXX">
                            @error('phone_number') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Blood Group -->
                        <div>
                            <label for="blood_group" class="block text-sm font-medium text-gray-700">Blood Group</label>
                            <select wire:model.blur="blood_group" id="blood_group" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Blood Group</option>
                                @foreach($bloodGroups as $bloodGroup)
                                    <option value="{{ $bloodGroup }}">{{ $bloodGroup }}</option>
                                @endforeach
                            </select>
                            @error('blood_group') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>
                </div>
            @endif

            <!-- Step 2: Address Information -->
            @if($currentStep === 2)
                <div class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Address & Identification</h3>
                    
                    <div class="space-y-6">
                        <!-- Present Address -->
                        <div>
                            <label for="present_address" class="block text-sm font-medium text-gray-700">Present Address *</label>
                            <textarea wire:model.blur="present_address" id="present_address" rows="3"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Enter current address with details"></textarea>
                            @error('present_address') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Permanent Address -->
                        <div>
                            <label for="permanent_address" class="block text-sm font-medium text-gray-700">Permanent Address *</label>
                            <textarea wire:model.blur="permanent_address" id="permanent_address" rows="3"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Enter permanent address with village, upazila, district"></textarea>
                            @error('permanent_address') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- NID Number -->
                            <div>
                                <label for="nid_number" class="block text-sm font-medium text-gray-700">NID Number *</label>
                                <input wire:model.blur="nid_number" type="text" id="nid_number" maxlength="13"
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="13-digit NID number">
                                @error('nid_number') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                            </div>

                            <!-- Occupation -->
                            <div>
                                <label for="occupation" class="block text-sm font-medium text-gray-700">Occupation *</label>
                                <input wire:model.blur="occupation" type="text" id="occupation" 
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter occupation">
                                @error('occupation') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Step 3: Photo and Reference -->
            @if($currentStep === 3)
                <div class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Photo & Reference</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Photo Upload -->
                        <div>
                            <label for="photo" class="block text-sm font-medium text-gray-700">Member Photo</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    @if($photo)
                                        <div class="mb-4">
                                            <img src="{{ $photo->temporaryUrl() }}" alt="Preview" 
                                                class="mx-auto h-32 w-32 object-cover rounded-lg">
                                        </div>
                                    @else
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    @endif
                                    <div class="flex text-sm text-gray-600">
                                        <label for="photo" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Upload a photo</span>
                                            <input wire:model="photo" id="photo" type="file" class="sr-only" accept="image/*">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                                </div>
                            </div>
                            @error('photo') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Reference Member -->
                        <div>
                            <label for="reference_id" class="block text-sm font-medium text-gray-700">Reference Member</label>
                            <select wire:model.blur="reference_id" id="reference_id" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">No Reference</option>
                                @foreach($members as $member)
                                    <option value="{{ $member->id }}">{{ $member->member_id }} - {{ $member->name }}</option>
                                @endforeach
                            </select>
                            @error('reference_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                            <p class="mt-1 text-sm text-gray-500">Optional: Select an existing member as reference</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Step 4: Branch Assignment -->
            @if($currentStep === 4)
                <div class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Branch Assignment</h3>
                    
                    <div>
                        <label for="branch_id" class="block text-sm font-medium text-gray-700">Assign to Branch *</label>
                        <select wire:model.blur="branch_id" id="branch_id" 
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            {{ Auth::user()->isFieldOfficer() ? 'disabled' : '' }}>
                            <option value="">Select Branch</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }} - {{ $branch->address }}</option>
                            @endforeach
                        </select>
                        @error('branch_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        
                        @if(Auth::user()->isFieldOfficer())
                            <p class="mt-1 text-sm text-gray-500">You can only register members to your assigned branch.</p>
                        @endif
                    </div>

                    <!-- Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Registration Summary</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">Name:</span>
                                <span class="ml-2 text-gray-900">{{ $name }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Phone:</span>
                                <span class="ml-2 text-gray-900">{{ $phone_number }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">NID:</span>
                                <span class="ml-2 text-gray-900">{{ $nid_number }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Occupation:</span>
                                <span class="ml-2 text-gray-900">{{ $occupation }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                <div>
                    @if($currentStep > 1)
                        <button type="button" wire:click="previousStep" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md font-medium transition duration-150 ease-in-out">
                            Previous
                        </button>
                    @endif
                </div>

                <div class="flex space-x-3">
                    @if($currentStep < $totalSteps)
                        <button type="button" wire:click="nextStep" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition duration-150 ease-in-out">
                            Next
                        </button>
                    @else
                        <button type="submit" 
                            class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium transition duration-150 ease-in-out">
                            Register Member
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </form>
</div>
