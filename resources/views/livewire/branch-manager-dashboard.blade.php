<div>
    <!-- Header Controls -->
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h3 class="text-lg font-medium text-gray-900">{{ $branchOverview['branch_name'] ?? 'Branch' }} Dashboard</h3>
            <p class="text-sm text-gray-500">Branch performance and management overview</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
            <select wire:model.live="selectedPeriod" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
            </select>
            <select wire:model.live="selectedOfficer" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="">All Officers</option>
                @foreach($fieldOfficers as $officer)
                    <option value="{{ $officer->id }}">{{ $officer->name }}</option>
                @endforeach
            </select>
            <button wire:click="exportReport" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-150">
                Export Report
            </button>
        </div>
    </div>

    <!-- Critical Alerts -->
    @if(count($alertsNotifications) > 0)
    <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-center mb-3">
            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <h4 class="text-sm font-medium text-yellow-800">Attention Required</h4>
        </div>
        <div class="space-y-2">
            @foreach($alertsNotifications as $alert)
                <div class="flex items-center justify-between text-sm">
                    <span class="text-yellow-700">{{ $alert['message'] }}</span>
                    <span class="text-yellow-600 font-medium">{{ $alert['action'] }}</span>
                </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Branch Overview KPIs -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Members</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($branchOverview['total_members'] ?? 0) }}</dd>
                            <dd class="text-xs text-green-600">+{{ $branchOverview['new_members_today'] ?? 0 }} today</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Collections</dt>
                            <dd class="text-lg font-medium text-gray-900">৳{{ number_format($branchOverview['total_collections'] ?? 0, 0) }}</dd>
                            <dd class="text-xs text-blue-600">৳{{ number_format($branchOverview['today_collections'] ?? 0, 0) }} today</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Loans</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($branchOverview['active_loans'] ?? 0) }}</dd>
                            <dd class="text-xs text-purple-600">{{ $branchOverview['collection_rate'] ?? 0 }}% collection rate</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Overdue Amount</dt>
                            <dd class="text-lg font-medium text-gray-900">৳{{ number_format($branchOverview['overdue_amount'] ?? 0, 0) }}</dd>
                            <dd class="text-xs text-red-600">{{ $branchOverview['total_officers'] ?? 0 }} officers</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary & Loan Portfolio -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Financial Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Income & Expense Summary</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">Collection Income</span>
                        <span class="text-sm font-bold text-green-600">৳{{ number_format($incomeExpenseSummary['collection_income'] ?? 0, 0) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">Branch Income</span>
                        <span class="text-sm font-bold text-blue-600">৳{{ number_format($incomeExpenseSummary['branch_income'] ?? 0, 0) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">Loan Disbursements</span>
                        <span class="text-sm font-bold text-red-600">৳{{ number_format($incomeExpenseSummary['loan_disbursements'] ?? 0, 0) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">Branch Expenses</span>
                        <span class="text-sm font-bold text-orange-600">৳{{ number_format($incomeExpenseSummary['branch_expenses'] ?? 0, 0) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-100 rounded-lg border-t-2 border-gray-300">
                        <span class="text-sm font-bold text-gray-900">Net Income</span>
                        <span class="text-sm font-bold {{ ($incomeExpenseSummary['net_income'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600' }}">
                            ৳{{ number_format($incomeExpenseSummary['net_income'] ?? 0, 0) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loan Portfolio Analysis -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Loan Portfolio Analysis</h3>
                <div class="space-y-4">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">৳{{ number_format($loanPortfolio['total_portfolio'] ?? 0, 0) }}</div>
                        <div class="text-sm text-gray-600">Total Portfolio</div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-3 bg-green-50 rounded-lg">
                            <div class="text-lg font-bold text-green-600">৳{{ number_format($loanPortfolio['paid_amount'] ?? 0, 0) }}</div>
                            <div class="text-xs text-gray-600">Recovered</div>
                        </div>
                        <div class="text-center p-3 bg-orange-50 rounded-lg">
                            <div class="text-lg font-bold text-orange-600">৳{{ number_format($loanPortfolio['outstanding_amount'] ?? 0, 0) }}</div>
                            <div class="text-xs text-gray-600">Outstanding</div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Recovery Rate</span>
                        <span class="text-sm font-bold text-green-600">{{ $loanPortfolio['recovery_rate'] ?? 0 }}%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">High Risk Loans</span>
                        <span class="text-sm font-bold text-red-600">{{ $loanPortfolio['high_risk_loans'] ?? 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">New Loans (Period)</span>
                        <span class="text-sm font-bold text-blue-600">{{ $loanPortfolio['new_loans'] ?? 0 }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Collection Efficiency Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Collection Efficiency Trends</h3>
            <div class="h-64">
                <canvas id="collectionEfficiencyChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Field Officers Performance -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Field Officers Performance</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Officer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collections</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members Added</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collection Count</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Activity</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($officerPerformance as $officer)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $officer['name'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">৳{{ number_format($officer['collections'], 0) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $officer['members_created'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $officer['collections_count'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        {{ $officer['efficiency'] >= 90 ? 'bg-green-100 text-green-800' : 
                                           ($officer['efficiency'] >= 70 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                        {{ $officer['efficiency'] }}%
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $officer['last_activity'] }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No field officers found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pending Loan Approvals -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Pending Loan Approvals</h3>
            <div class="space-y-4">
                @forelse($pendingApprovals as $application)
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <div class="font-medium text-gray-900">{{ $application['member_name'] }}</div>
                                <span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">{{ $application['member_id'] }}</span>
                                <span class="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">{{ $application['days_pending'] }} days</span>
                            </div>
                            <div class="text-sm text-gray-500 mt-1">
                                Amount: ৳{{ number_format($application['applied_amount'], 0) }} • Reason: {{ $application['reason'] }}
                            </div>
                            <div class="text-xs text-gray-400 mt-1">
                                Applied: {{ $application['applied_at'] }}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button wire:click="approveLoan({{ $application['id'] }})" 
                                    class="px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700">
                                Approve
                            </button>
                            <button wire:click="rejectLoan({{ $application['id'] }})" 
                                    class="px-3 py-1 bg-red-600 text-white text-xs rounded-md hover:bg-red-700">
                                Reject
                            </button>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8 text-gray-500">
                        <svg class="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p>No pending loan applications</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="{{ route('manager.loan-approvals') }}" 
           class="flex items-center justify-center p-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">Loan Approvals</span>
        </a>
        
        <a href="{{ route('manager.members') }}" 
           class="flex items-center justify-center p-6 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-150">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <span class="font-medium">Manage Members</span>
        </a>
        
        <a href="{{ route('manager.installment-reports') }}" 
           class="flex items-center justify-center p-6 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-150">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span class="font-medium">View Reports</span>
        </a>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('collectionEfficiencyChart').getContext('2d');
    const efficiencyData = @json($collectionEfficiency);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: efficiencyData.map(item => item.date),
            datasets: [{
                label: 'Due Amount (৳)',
                data: efficiencyData.map(item => item.due),
                borderColor: 'rgb(239, 68, 68)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.4,
                fill: false,
                yAxisID: 'y'
            }, {
                label: 'Collected Amount (৳)',
                data: efficiencyData.map(item => item.collected),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: false,
                yAxisID: 'y'
            }, {
                label: 'Efficiency Rate (%)',
                data: efficiencyData.map(item => item.rate),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Amount (৳)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '৳' + value.toLocaleString();
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Efficiency (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
});
</script>
@endpush
