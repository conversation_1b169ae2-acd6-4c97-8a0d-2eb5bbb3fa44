<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Advertisement Management</h2>
                <p class="text-gray-600">Manage advertisements with advanced scheduling and analytics</p>
            </div>
            <button wire:click="openModal"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span>Create Advertisement</span>
            </button>
        </div>
    </div>

    <!-- Analytics Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        @php
            $analytics = $this->getAnalytics();
        @endphp

        @foreach($analytics as $stat)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ ucfirst(str_replace('_', ' ', $stat->placement)) }}</p>
                        <p class="text-lg font-bold text-gray-900">{{ $stat->total_ads }} ads</p>
                        <p class="text-xs text-gray-500">CTR: {{ number_format($stat->avg_ctr ?? 0, 2) }}%</p>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" wire:model.live="search"
                       placeholder="Search advertisements..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Placement</label>
                <select wire:model.live="filter_placement"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Placements</option>
                    @foreach($placements as $key => $label)
                        <option value="{{ $key }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select wire:model.live="filter_status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>

            <div class="flex items-end">
                <button wire:click="clearFilters"
                        class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md">
                    Clear Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Advertisements Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Advertisement</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Placement</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($advertisements as $ad)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    @if($ad->image)
                                        <img class="h-12 w-12 rounded-lg object-cover" src="{{ $ad->image_url }}" alt="{{ $ad->title }}">
                                    @else
                                        <div class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    @endif
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $ad->title }}</div>
                                        <div class="text-sm text-gray-500">{{ Str::limit($ad->description, 50) }}</div>
                                        <div class="text-xs text-gray-400">Order: {{ $ad->display_order }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ $ad->placement_label }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @if($ad->start_date)
                                    <div>Start: {{ $ad->start_date->format('M d, Y') }}</div>
                                @endif
                                @if($ad->end_date)
                                    <div>End: {{ $ad->end_date->format('M d, Y') }}</div>
                                @endif
                                @if(!$ad->start_date && !$ad->end_date)
                                    <span class="text-gray-500">No schedule</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>Clicks: {{ number_format($ad->click_count) }}</div>
                                <div>Views: {{ number_format($ad->impression_count) }}</div>
                                <div class="text-xs text-gray-500">CTR: {{ number_format($ad->click_through_rate, 2) }}%</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                           {{ $ad->isCurrentlyActive() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $ad->status_label }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button wire:click="editAdvertisement({{ $ad->id }})"
                                            class="text-blue-600 hover:text-blue-900">
                                        Edit
                                    </button>
                                    <button wire:click="toggleStatus({{ $ad->id }})"
                                            class="text-{{ $ad->active ? 'red' : 'green' }}-600 hover:text-{{ $ad->active ? 'red' : 'green' }}-900">
                                        {{ $ad->active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                    <button wire:click="duplicateAdvertisement({{ $ad->id }})"
                                            class="text-purple-600 hover:text-purple-900">
                                        Duplicate
                                    </button>
                                    <button wire:click="deleteAdvertisement({{ $ad->id }})"
                                            onclick="return confirm('Are you sure you want to delete this advertisement?')"
                                            class="text-red-600 hover:text-red-900">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                No advertisements found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-3 border-t border-gray-200">
            {{ $advertisements->links() }}
        </div>
    </div>
</div>
