<div class="max-w-7xl mx-auto p-6">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Installment Collection</h2>
        <p class="text-gray-600">Daily collection interface for field officers</p>
    </div>

    @if (session()->has('success'))
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {{ session('error') }}
        </div>
    @endif

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Today's Collections</p>
                    <p class="text-2xl font-semibold text-gray-900">৳{{ number_format($today_collections, 2) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Today's Target</p>
                    <p class="text-2xl font-semibold text-gray-900">৳{{ number_format($today_target, 2) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $overdue_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Collection Rate</p>
                    <p class="text-2xl font-semibold text-gray-900">
                        {{ $today_target > 0 ? number_format(($today_collections / $today_target) * 100, 1) : 0 }}%
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Member Search -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Member Search</h3>
        
        @if(!$selected_member)
            <div class="relative">
                <input type="text" 
                       wire:model.live="member_search" 
                       placeholder="Search member by name, ID, or phone..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                
                @if(count($members) > 0)
                    <div class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        @foreach($members as $member)
                            <div wire:click="selectMember({{ $member->id }})" 
                                 class="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b">
                                <div class="font-medium">{{ $member->name }}</div>
                                <div class="text-sm text-gray-600">ID: {{ $member->member_id }} | Phone: {{ $member->phone_number }}</div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        @else
            <div class="flex items-center justify-between bg-blue-50 p-3 rounded-md mb-4">
                <div>
                    <div class="font-medium">{{ $selected_member->name }}</div>
                    <div class="text-sm text-gray-600">ID: {{ $selected_member->member_id }} | Phone: {{ $selected_member->phone_number }}</div>
                </div>
                <button wire:click="clearMemberSelection" class="text-red-600 hover:text-red-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Member Loans -->
            @if(count($member_loans) > 0)
                <div class="space-y-4">
                    @foreach($member_loans as $loan)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-medium">Loan #{{ $loan->id }} - ৳{{ number_format($loan->loan_amount, 2) }}</h4>
                                <span class="text-sm text-gray-500">{{ $loan->repayment_method }}</span>
                            </div>
                            
                            <div class="space-y-2">
                                @foreach($loan->installments as $installment)
                                    <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                        <div class="flex-1">
                                            <span class="text-sm">Installment #{{ $installment->installment_no }}</span>
                                            <span class="text-sm text-gray-600 ml-2">{{ $installment->installment_date->format('M d, Y') }}</span>
                                            @if($installment->is_overdue)
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 ml-2">
                                                    Overdue
                                                </span>
                                            @endif
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="font-medium">৳{{ number_format($installment->installment_amount, 2) }}</span>
                                            <button wire:click="openCollectionModal({{ $installment->id }})" 
                                                    class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                                                Collect
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-center py-4">No pending installments for this member.</p>
            @endif
        @endif
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select wire:model.live="status_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="pending">Pending</option>
                    <option value="due_today">Due Today</option>
                    <option value="overdue">Overdue</option>
                    <option value="paid">Collected</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" wire:model.live="collection_date" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" wire:model.live="search" placeholder="Search member..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div class="flex items-end">
                @if(count($selected_installments) > 0)
                    <button wire:click="bulkCollect" 
                            class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                        Bulk Collect ({{ count($selected_installments) }})
                    </button>
                @endif
            </div>
        </div>
    </div>

    <!-- Installments Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            @if($status_filter === 'pending')
                                <input type="checkbox" wire:model.live="select_all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            @endif
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Installment</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($installments as $installment)
                        @php
                            $member = $installment->loan->loanApplication->member;
                            $isOverdue = $installment->is_overdue;
                        @endphp
                        <tr class="hover:bg-gray-50 {{ $isOverdue ? 'bg-red-50' : '' }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($installment->status === 'pending')
                                    <input type="checkbox" wire:model.live="selected_installments" value="{{ $installment->id }}" 
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $member->name }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ $member->member_id }}</div>
                                        <div class="text-sm text-gray-500">{{ $member->phone_number }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">Loan #{{ $installment->loan->id }}</div>
                                <div class="text-sm text-gray-500">Installment #{{ $installment->installment_no }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">৳{{ number_format($installment->installment_amount, 2) }}</div>
                                @if($installment->due > 0)
                                    <div class="text-sm text-red-500">Due: ৳{{ number_format($installment->due, 2) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $installment->installment_date->format('M d, Y') }}
                                @if($isOverdue)
                                    <div class="text-xs text-red-500">
                                        {{ $installment->installment_date->diffForHumans() }}
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($installment->status === 'pending')
                                    @if($isOverdue)
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Overdue
                                        </span>
                                    @else
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    @endif
                                @elseif($installment->status === 'paid')
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Paid
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        Partial
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                @if($installment->status === 'pending')
                                    <button wire:click="openCollectionModal({{ $installment->id }})" 
                                            class="text-green-600 hover:text-green-900">
                                        Collect
                                    </button>
                                    @if($isOverdue)
                                        <button wire:click="markAsOverdue({{ $installment->id }})" 
                                                class="text-red-600 hover:text-red-900">
                                            Mark Overdue
                                        </button>
                                    @endif
                                @else
                                    <span class="text-gray-400">
                                        Collected by {{ $installment->collector->name ?? 'System' }}
                                    </span>
                                    @if($installment->collection_date)
                                        <div class="text-xs text-gray-500">{{ $installment->collection_date->format('M d, Y H:i') }}</div>
                                    @endif
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No installments found
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-3 border-t border-gray-200">
            {{ $installments->links() }}
        </div>
    </div>

    <!-- Collection Modal -->
    @if($show_collection_modal && $collecting_installment)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            Collect Installment - {{ $collecting_installment->loan->loanApplication->member->name }}
                        </h3>
                        <button wire:click="closeCollectionModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Installment Details -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Loan ID:</span>
                                <div class="font-medium">#{{ $collecting_installment->loan->id }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Installment No:</span>
                                <div class="font-medium">#{{ $collecting_installment->installment_no }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Due Date:</span>
                                <div class="font-medium">{{ $collecting_installment->installment_date->format('M d, Y') }}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Installment Amount:</span>
                                <div class="font-medium">৳{{ number_format($collecting_installment->installment_amount, 2) }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Collection Form -->
                    <div class="space-y-4">
                        <div>
                            <div class="flex items-center mb-2">
                                <input type="checkbox" wire:model.live="partial_payment" id="partial_payment"
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <label for="partial_payment" class="ml-2 text-sm font-medium text-gray-700">
                                    Partial Payment
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Collection Amount (৳)</label>
                            <input type="number" wire:model="collection_amount"
                                   min="1"
                                   max="{{ $collecting_installment->installment_amount }}"
                                   step="0.01"
                                   {{ !$partial_payment ? 'readonly' : '' }}
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 {{ !$partial_payment ? 'bg-gray-100' : '' }}">
                            @error('collection_amount') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Collection Notes (Optional)</label>
                            <textarea wire:model="collection_notes" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="Add any notes about this collection..."></textarea>
                            @error('collection_notes') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        @if($partial_payment && $collection_amount)
                            <div class="bg-yellow-50 p-3 rounded-lg">
                                <div class="text-sm">
                                    <span class="text-gray-600">Collecting:</span>
                                    <span class="font-medium">৳{{ number_format($collection_amount, 2) }}</span>
                                </div>
                                <div class="text-sm">
                                    <span class="text-gray-600">Remaining Due:</span>
                                    <span class="font-medium text-red-600">৳{{ number_format($collecting_installment->installment_amount - $collection_amount, 2) }}</span>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Modal Actions -->
                    <div class="flex justify-end space-x-4 mt-6">
                        <button wire:click="closeCollectionModal"
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Cancel
                        </button>
                        <button wire:click="collectInstallment"
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                                wire:loading.attr="disabled">
                            <span wire:loading.remove>Collect Payment</span>
                            <span wire:loading>Processing...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
