<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Member Management</h1>
                <p class="text-gray-600">Manage member profiles and information</p>
            </div>
            <div class="flex space-x-3">
                <button wire:click="exportMembers" 
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition duration-150 ease-in-out">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
                <a href="{{ route('members.register') }}" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition duration-150 ease-in-out">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Member
                </a>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('message') }}</span>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="p-4">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <!-- Search and Filters -->
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                    <!-- Search -->
                    <div class="relative">
                        <input wire:model.live="search" type="text" placeholder="Search members..."
                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Branch Filter -->
                    @if(!Auth::user()->isFieldOfficer())
                        <select wire:model.live="branchFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All Branches</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                    @endif

                    <!-- Status Filter -->
                    <select wire:model.live="statusFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">All Status</option>
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>
            </div>

            <!-- Bulk Actions -->
            @if(count($selectedMembers) > 0)
                <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-blue-700">{{ count($selectedMembers) }} member(s) selected</span>
                        <div class="flex space-x-2">
                            <button wire:click="bulkActivate" 
                                class="text-sm bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded">
                                Activate
                            </button>
                            <button wire:click="bulkDeactivate" 
                                class="text-sm bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded">
                                Deactivate
                            </button>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Members Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" wire:model.live="selectAll" 
                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            wire:click="sortBy('member_id')">
                            <div class="flex items-center">
                                Member ID
                                @if($sortBy === 'member_id')
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        @if($sortDirection === 'asc')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                        @else
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        @endif
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            wire:click="sortBy('name')">
                            <div class="flex items-center">
                                Member Info
                                @if($sortBy === 'name')
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        @if($sortDirection === 'asc')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                        @else
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        @endif
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            wire:click="sortBy('created_at')">
                            <div class="flex items-center">
                                Joined
                                @if($sortBy === 'created_at')
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        @if($sortDirection === 'asc')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                        @else
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        @endif
                                    </svg>
                                @endif
                            </div>
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($members as $member)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <input type="checkbox" wire:model.live="selectedMembers" value="{{ $member->id }}"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ $member->member_id }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @if($member->photo)
                                            <img src="{{ $member->photo_url }}" alt="{{ $member->name }}" 
                                                class="h-10 w-10 rounded-full object-cover">
                                        @else
                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700">
                                                    {{ strtoupper(substr($member->name, 0, 2)) }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $member->name }}</div>
                                        <div class="text-sm text-gray-500">S/O: {{ $member->father_or_husband_name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $member->phone_number }}</div>
                                <div class="text-sm text-gray-500">{{ $member->occupation }}</div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {{ $member->branch?->name ?? 'No Branch' }}
                            </td>
                            <td class="px-6 py-4">
                                <button wire:click="toggleMemberStatus({{ $member->id }})"
                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full cursor-pointer
                                    {{ $member->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $member->is_active ? 'Active' : 'Inactive' }}
                                </button>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {{ $member->created_at->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button wire:click="viewMember({{ $member->id }})"
                                        class="text-blue-600 hover:text-blue-900">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                    <button wire:click="editMember({{ $member->id }})"
                                        class="text-green-600 hover:text-green-900">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button wire:click="deleteMember({{ $member->id }})"
                                        onclick="return confirm('Are you sure you want to delete this member?')"
                                        class="text-red-600 hover:text-red-900">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No members found</h3>
                                <p class="mt-1 text-sm text-gray-500">Get started by registering a new member.</p>
                                <div class="mt-6">
                                    <a href="{{ route('members.register') }}" 
                                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Register Member
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($members->hasPages())
            <div class="px-6 py-3 border-t border-gray-200">
                {{ $members->links() }}
            </div>
        @endif
    </div>

    <!-- View Member Modal -->
    @if($showViewModal && $selectedMember)
        <div class="fixed inset-0 z-50 overflow-y-auto" x-data="{ show: @entangle('showViewModal') }" x-show="show">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" x-show="show"></div>
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full" x-show="show">

                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-medium text-gray-900">Member Profile</h3>
                            <button wire:click="closeViewModal" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Member Photo and Basic Info -->
                            <div class="lg:col-span-1">
                                <div class="text-center">
                                    @if($selectedMember->photo)
                                        <img src="{{ $selectedMember->photo_url }}" alt="{{ $selectedMember->name }}"
                                            class="mx-auto h-32 w-32 rounded-full object-cover">
                                    @else
                                        <div class="mx-auto h-32 w-32 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-2xl font-medium text-gray-700">
                                                {{ strtoupper(substr($selectedMember->name, 0, 2)) }}
                                            </span>
                                        </div>
                                    @endif
                                    <h4 class="mt-4 text-xl font-semibold text-gray-900">{{ $selectedMember->name }}</h4>
                                    <p class="text-gray-600">{{ $selectedMember->member_id }}</p>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-2
                                        {{ $selectedMember->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $selectedMember->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>

                                <!-- Quick Stats -->
                                <div class="mt-6 space-y-3">
                                    <div class="bg-blue-50 p-3 rounded-lg">
                                        <div class="text-sm text-blue-600">Active Loans</div>
                                        <div class="text-lg font-semibold text-blue-900">{{ $selectedMember->activeLoans->count() }}</div>
                                    </div>
                                    <div class="bg-green-50 p-3 rounded-lg">
                                        <div class="text-sm text-green-600">Savings Accounts</div>
                                        <div class="text-lg font-semibold text-green-900">{{ $selectedMember->activeSavingAccounts->count() }}</div>
                                    </div>
                                    <div class="bg-yellow-50 p-3 rounded-lg">
                                        <div class="text-sm text-yellow-600">Pending Applications</div>
                                        <div class="text-lg font-semibold text-yellow-900">{{ $selectedMember->pendingLoanApplications->count() }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Member Details -->
                            <div class="lg:col-span-2">
                                <div class="space-y-6">
                                    <!-- Personal Information -->
                                    <div>
                                        <h5 class="text-lg font-medium text-gray-900 mb-3">Personal Information</h5>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-500">Father/Husband:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->father_or_husband_name }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Mother:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->mother_name }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Date of Birth:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->date_of_birth->format('F j, Y') }} ({{ $selectedMember->age }} years)</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Religion:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->religion }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Blood Group:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->blood_group ?? 'Not specified' }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Occupation:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->occupation }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Contact Information -->
                                    <div>
                                        <h5 class="text-lg font-medium text-gray-900 mb-3">Contact Information</h5>
                                        <div class="space-y-2 text-sm">
                                            <div>
                                                <span class="text-gray-500">Phone:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->phone_number }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">NID:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->nid_number }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Present Address:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->present_address }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Permanent Address:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->permanent_address }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- System Information -->
                                    <div>
                                        <h5 class="text-lg font-medium text-gray-900 mb-3">System Information</h5>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-500">Branch:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->branch?->name ?? 'No Branch' }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Reference:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->reference?->name ?? 'No Reference' }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Created By:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->creator?->name ?? 'System' }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">Joined:</span>
                                                <span class="ml-2 text-gray-900">{{ $selectedMember->created_at->format('F j, Y') }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Recent Activity -->
                                    <div>
                                        <h5 class="text-lg font-medium text-gray-900 mb-3">Recent Activity</h5>
                                        <div class="space-y-2">
                                            @forelse($selectedMember->loanApplications->take(3) as $application)
                                                <div class="flex items-center text-sm">
                                                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                                                    <span class="text-gray-600">
                                                        Loan application for ৳{{ number_format($application->applied_amount, 2) }} -
                                                        <span class="font-medium">{{ ucfirst($application->status) }}</span>
                                                    </span>
                                                    <span class="ml-auto text-gray-400">{{ $application->applied_at->diffForHumans() }}</span>
                                                </div>
                                            @empty
                                                <div class="text-sm text-gray-500">No recent activity</div>
                                            @endforelse
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button wire:click="editMember({{ $selectedMember->id }})"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Edit Member
                        </button>
                        <button wire:click="closeViewModal"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Edit Member Modal -->
    @if($showEditModal && $selectedMember)
        <div class="fixed inset-0 z-50 overflow-y-auto" x-data="{ show: @entangle('showEditModal') }" x-show="show">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" x-show="show"></div>
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full" x-show="show">

                    <form wire:submit="updateMember">
                        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-medium text-gray-900">Edit Member</h3>
                                <button type="button" wire:click="closeEditModal" class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                <!-- Photo Section -->
                                <div class="lg:col-span-1">
                                    <div class="text-center">
                                        @if($selectedMember->photo)
                                            <img src="{{ $selectedMember->photo_url }}" alt="{{ $selectedMember->name }}"
                                                class="mx-auto h-32 w-32 rounded-full object-cover mb-4">
                                        @else
                                            <div class="mx-auto h-32 w-32 rounded-full bg-gray-300 flex items-center justify-center mb-4">
                                                <span class="text-2xl font-medium text-gray-700">
                                                    {{ strtoupper(substr($selectedMember->name, 0, 2)) }}
                                                </span>
                                            </div>
                                        @endif

                                        <!-- Photo Upload -->
                                        <div>
                                            <label for="newPhoto" class="block text-sm font-medium text-gray-700 mb-2">
                                                Update Photo
                                            </label>
                                            <input wire:model="newPhoto" type="file" id="newPhoto" accept="image/*"
                                                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                            @error('newPhoto')
                                                <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        @if($newPhoto)
                                            <div class="mt-4">
                                                <p class="text-sm text-gray-600 mb-2">Preview:</p>
                                                <img src="{{ $newPhoto->temporaryUrl() }}" alt="Preview"
                                                    class="mx-auto w-20 h-20 rounded-full object-cover border-2 border-gray-200">
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Form Fields -->
                                <div class="lg:col-span-2">
                                    <div class="space-y-6">
                                        <!-- Basic Information -->
                                        <div>
                                            <h5 class="text-lg font-medium text-gray-900 mb-3">Basic Information</h5>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <label for="edit_name" class="block text-sm font-medium text-gray-700">Full Name *</label>
                                                    <input wire:model="name" type="text" id="edit_name"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                    @error('name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_father_or_husband_name" class="block text-sm font-medium text-gray-700">Father/Husband Name *</label>
                                                    <input wire:model="father_or_husband_name" type="text" id="edit_father_or_husband_name"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                    @error('father_or_husband_name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_mother_name" class="block text-sm font-medium text-gray-700">Mother's Name *</label>
                                                    <input wire:model="mother_name" type="text" id="edit_mother_name"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                    @error('mother_name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth *</label>
                                                    <input wire:model="date_of_birth" type="date" id="edit_date_of_birth"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                    @error('date_of_birth') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_religion" class="block text-sm font-medium text-gray-700">Religion *</label>
                                                    <select wire:model="religion" id="edit_religion"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                        <option value="">Select Religion</option>
                                                        @foreach($religions as $religionOption)
                                                            <option value="{{ $religionOption }}">{{ $religionOption }}</option>
                                                        @endforeach
                                                    </select>
                                                    @error('religion') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_phone_number" class="block text-sm font-medium text-gray-700">Phone Number *</label>
                                                    <input wire:model="phone_number" type="tel" id="edit_phone_number"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                    @error('phone_number') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_blood_group" class="block text-sm font-medium text-gray-700">Blood Group</label>
                                                    <select wire:model="blood_group" id="edit_blood_group"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                        <option value="">Select Blood Group</option>
                                                        @foreach($bloodGroups as $bloodGroup)
                                                            <option value="{{ $bloodGroup }}">{{ $bloodGroup }}</option>
                                                        @endforeach
                                                    </select>
                                                    @error('blood_group') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_occupation" class="block text-sm font-medium text-gray-700">Occupation *</label>
                                                    <input wire:model="occupation" type="text" id="edit_occupation"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                    @error('occupation') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Address Information -->
                                        <div>
                                            <h5 class="text-lg font-medium text-gray-900 mb-3">Address Information</h5>
                                            <div class="space-y-4">
                                                <div>
                                                    <label for="edit_present_address" class="block text-sm font-medium text-gray-700">Present Address *</label>
                                                    <textarea wire:model="present_address" id="edit_present_address" rows="2"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"></textarea>
                                                    @error('present_address') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_permanent_address" class="block text-sm font-medium text-gray-700">Permanent Address *</label>
                                                    <textarea wire:model="permanent_address" id="edit_permanent_address" rows="2"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"></textarea>
                                                    @error('permanent_address') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <!-- System Information -->
                                        <div>
                                            <h5 class="text-lg font-medium text-gray-900 mb-3">System Information</h5>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <label for="edit_reference_id" class="block text-sm font-medium text-gray-700">Reference Member</label>
                                                    <select wire:model="reference_id" id="edit_reference_id"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                                        <option value="">No Reference</option>
                                                        @foreach($allMembers as $member)
                                                            @if($member->id !== $selectedMember->id)
                                                                <option value="{{ $member->id }}">{{ $member->member_id }} - {{ $member->name }}</option>
                                                            @endif
                                                        @endforeach
                                                    </select>
                                                    @error('reference_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div>
                                                    <label for="edit_branch_id" class="block text-sm font-medium text-gray-700">Branch *</label>
                                                    <select wire:model="branch_id" id="edit_branch_id"
                                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                        {{ Auth::user()->isFieldOfficer() ? 'disabled' : '' }}>
                                                        <option value="">Select Branch</option>
                                                        @foreach($branches as $branch)
                                                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                                        @endforeach
                                                    </select>
                                                    @error('branch_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                                </div>

                                                <div class="md:col-span-2">
                                                    <div class="flex items-center">
                                                        <input wire:model="is_active" type="checkbox" id="edit_is_active"
                                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                                        <label for="edit_is_active" class="ml-2 block text-sm text-gray-700">Active Member</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                            <button type="submit"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                                Update Member
                            </button>
                            <button type="button" wire:click="closeEditModal"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
