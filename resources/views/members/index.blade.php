<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Member Management') }}
            </h2>
            <nav class="text-sm text-gray-500">
                @if(Auth::user()->isAdmin())
                    <a href="{{ route('admin.dashboard') }}" class="hover:text-gray-700">Dashboard</a>
                @elseif(Auth::user()->isManager())
                    <a href="{{ route('manager.dashboard') }}" class="hover:text-gray-700">Dashboard</a>
                @else
                    <a href="{{ route('officer.dashboard') }}" class="hover:text-gray-700">Dashboard</a>
                @endif
                <span class="mx-2">/</span>
                <span class="text-gray-900">Members</span>
            </nav>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <livewire:member-management />
        </div>
    </div>
</x-app-layout>
