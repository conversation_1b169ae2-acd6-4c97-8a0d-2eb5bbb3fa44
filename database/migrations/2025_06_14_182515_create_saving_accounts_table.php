<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('saving_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('member_id');
            $table->enum('saving_type', ['general', 'dps', 'fdr']);
            $table->string('joint_photo')->nullable();
            $table->string('nominee_name');
            $table->string('nominee_relation');
            $table->enum('saving_method', ['daily', 'weekly']);
            $table->decimal('monthly_amount', 10, 2);
            $table->decimal('fdr_amount', 10, 2)->nullable();
            $table->date('start_date');
            $table->unsignedBigInteger('created_by');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['member_id', 'saving_type', 'is_active']);
            $table->index('created_by');
            $table->index('start_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('saving_accounts');
    }
};
