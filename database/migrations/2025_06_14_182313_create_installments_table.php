<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('installments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_id');
            $table->integer('installment_no');
            $table->date('installment_date');
            $table->decimal('installment_amount', 10, 2);
            $table->decimal('advance_paid', 10, 2)->default(0);
            $table->decimal('due', 10, 2)->default(0);
            $table->unsignedBigInteger('collected_by')->nullable();
            $table->date('collection_date')->nullable();
            $table->enum('status', ['pending', 'paid', 'overdue'])->default('pending');
            $table->timestamps();

            $table->index(['loan_id', 'installment_no']);
            $table->index(['status', 'installment_date']);
            $table->index('collected_by');
            $table->index('collection_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('installments');
    }
};
