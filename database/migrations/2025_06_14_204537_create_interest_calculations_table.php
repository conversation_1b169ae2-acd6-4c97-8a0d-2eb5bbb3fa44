<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interest_calculations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('saving_account_id');
            $table->string('calculation_period'); // monthly, quarterly, yearly
            $table->date('period_start');
            $table->date('period_end');
            $table->decimal('principal_amount', 12, 2);
            $table->decimal('interest_rate', 5, 2); // Percentage
            $table->decimal('calculated_interest', 10, 2);
            $table->decimal('tax_deducted', 10, 2)->default(0);
            $table->decimal('net_interest', 10, 2);
            $table->date('posting_date')->nullable();
            $table->enum('status', ['calculated', 'posted', 'cancelled'])->default('calculated');
            $table->unsignedBigInteger('calculated_by');
            $table->unsignedBigInteger('posted_by')->nullable();
            $table->json('calculation_details')->nullable(); // Store detailed calculation breakdown
            $table->timestamps();

            $table->index(['saving_account_id', 'calculation_period']);
            $table->index(['period_start', 'period_end']);
            $table->index('status');
            $table->foreign('saving_account_id')->references('id')->on('saving_accounts');
            $table->foreign('calculated_by')->references('id')->on('users');
            $table->foreign('posted_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interest_calculations');
    }
};
