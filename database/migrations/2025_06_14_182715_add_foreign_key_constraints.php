<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraints for users table
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('member_id')->references('id')->on('members')->onDelete('set null');
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('set null');
        });

        // Add foreign key constraints for branches table
        Schema::table('branches', function (Blueprint $table) {
            $table->foreign('manager_id')->references('id')->on('users')->onDelete('set null');
        });

        // Add foreign key constraints for members table
        Schema::table('members', function (Blueprint $table) {
            $table->foreign('reference_id')->references('id')->on('members')->onDelete('set null');
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });

        // Add foreign key constraints for loan_applications table
        Schema::table('loan_applications', function (Blueprint $table) {
            $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
            $table->foreign('reviewed_by')->references('id')->on('users')->onDelete('set null');
        });

        // Add foreign key constraints for loans table
        Schema::table('loans', function (Blueprint $table) {
            $table->foreign('loan_application_id')->references('id')->on('loan_applications')->onDelete('cascade');
        });

        // Add foreign key constraints for installments table
        Schema::table('installments', function (Blueprint $table) {
            $table->foreign('loan_id')->references('id')->on('loans')->onDelete('cascade');
            $table->foreign('collected_by')->references('id')->on('users')->onDelete('set null');
        });

        // Add foreign key constraints for branch_transactions table
        Schema::table('branch_transactions', function (Blueprint $table) {
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('cascade');
            $table->foreign('entered_by')->references('id')->on('users')->onDelete('cascade');
        });

        // Add foreign key constraints for saving_accounts table
        Schema::table('saving_accounts', function (Blueprint $table) {
            $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints in reverse order
        Schema::table('saving_accounts', function (Blueprint $table) {
            $table->dropForeign(['member_id']);
            $table->dropForeign(['created_by']);
        });

        Schema::table('branch_transactions', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['entered_by']);
        });

        Schema::table('installments', function (Blueprint $table) {
            $table->dropForeign(['loan_id']);
            $table->dropForeign(['collected_by']);
        });

        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['loan_application_id']);
        });

        Schema::table('loan_applications', function (Blueprint $table) {
            $table->dropForeign(['member_id']);
            $table->dropForeign(['reviewed_by']);
        });

        Schema::table('members', function (Blueprint $table) {
            $table->dropForeign(['reference_id']);
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['created_by']);
        });

        Schema::table('branches', function (Blueprint $table) {
            $table->dropForeign(['manager_id']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['member_id']);
            $table->dropForeign(['branch_id']);
        });
    }
};
