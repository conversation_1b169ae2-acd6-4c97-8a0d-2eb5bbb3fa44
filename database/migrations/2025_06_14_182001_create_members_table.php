<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('members', function (Blueprint $table) {
            $table->id();
            $table->string('member_id')->unique();
            $table->string('name');
            $table->string('father_or_husband_name');
            $table->string('mother_name');
            $table->text('present_address');
            $table->text('permanent_address');
            $table->string('nid_number')->unique();
            $table->date('date_of_birth');
            $table->string('religion');
            $table->string('phone_number');
            $table->string('blood_group')->nullable();
            $table->string('photo')->nullable();
            $table->string('occupation');
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->unsignedBigInteger('branch_id');
            $table->unsignedBigInteger('created_by');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['branch_id', 'is_active']);
            $table->index('member_id');
            $table->index('nid_number');
            $table->index('created_by');
            $table->index('reference_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('members');
    }
};
