<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            $table->text('description')->nullable()->after('title');
            $table->integer('display_order')->default(0)->after('active');
            $table->date('start_date')->nullable()->after('display_order');
            $table->date('end_date')->nullable()->after('start_date');
            $table->integer('click_count')->default(0)->after('end_date');
            $table->integer('impression_count')->default(0)->after('click_count');
            $table->string('target_audience')->nullable()->after('impression_count');
            $table->string('placement')->default('login_page')->after('target_audience');
            $table->unsignedBigInteger('created_by')->nullable()->after('placement');

            $table->index(['active', 'placement', 'display_order']);
            $table->index(['start_date', 'end_date']);
            $table->foreign('created_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropIndex(['active', 'placement', 'display_order']);
            $table->dropIndex(['start_date', 'end_date']);

            $table->dropColumn([
                'description',
                'display_order',
                'start_date',
                'end_date',
                'click_count',
                'impression_count',
                'target_audience',
                'placement',
                'created_by',
            ]);
        });
    }
};
