<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_application_id')->unique();
            $table->date('loan_date');
            $table->decimal('loan_amount', 10, 2);
            $table->decimal('total_repayment_amount', 10, 2);
            $table->string('repayment_duration');
            $table->enum('repayment_method', ['weekly', 'monthly']);
            $table->integer('installment_count');
            $table->decimal('installment_amount', 10, 2);
            $table->decimal('advance_payment', 10, 2)->default(0);
            $table->date('first_installment_date');
            $table->date('last_installment_date');
            $table->timestamps();

            $table->index('loan_application_id');
            $table->index(['loan_date', 'repayment_method']);
            $table->index('first_installment_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loans');
    }
};
