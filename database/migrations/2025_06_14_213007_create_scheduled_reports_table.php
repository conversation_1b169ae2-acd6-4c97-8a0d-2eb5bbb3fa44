<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scheduled_reports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('template_id');
            $table->string('name');
            $table->string('frequency'); // daily, weekly, monthly, quarterly, yearly
            $table->json('recipients'); // Email addresses
            $table->json('filters')->nullable(); // Custom filters for this schedule
            $table->time('schedule_time')->default('09:00:00');
            $table->integer('schedule_day')->nullable(); // Day of week/month
            $table->date('next_run_date');
            $table->date('last_run_date')->nullable();
            $table->enum('status', ['active', 'paused', 'completed', 'failed'])->default('active');
            $table->text('last_error')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->index(['frequency', 'status', 'next_run_date']);
            $table->index('template_id');
            $table->index('created_by');
            $table->foreign('template_id')->references('id')->on('report_templates');
            $table->foreign('created_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scheduled_reports');
    }
};
