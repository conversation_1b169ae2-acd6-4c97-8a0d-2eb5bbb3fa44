<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branch_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->enum('entry_type', ['income', 'expense']);
            $table->unsignedInteger('serial_no');
            $table->date('date');
            $table->text('description');
            $table->string('account_no');
            $table->string('category');
            $table->string('voucher_no')->nullable();
            $table->decimal('amount', 10, 2);
            $table->unsignedBigInteger('entered_by');
            $table->timestamps();

            $table->index(['branch_id', 'entry_type', 'date']);
            $table->index(['date', 'entry_type']);
            $table->index('entered_by');
            $table->index('serial_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_transactions');
    }
};
