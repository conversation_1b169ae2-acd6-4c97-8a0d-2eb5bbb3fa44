<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('saving_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('saving_account_id');
            $table->enum('transaction_type', ['deposit', 'withdrawal', 'interest', 'fee', 'penalty']);
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->date('transaction_date');
            $table->text('description')->nullable();
            $table->string('reference_no')->nullable();
            $table->unsignedBigInteger('processed_by');
            $table->enum('status', ['pending', 'completed', 'cancelled'])->default('completed');
            $table->json('metadata')->nullable(); // For storing additional transaction details
            $table->timestamps();

            $table->index(['saving_account_id', 'transaction_date']);
            $table->index(['transaction_type', 'status']);
            $table->index('processed_by');
            $table->foreign('saving_account_id')->references('id')->on('saving_accounts');
            $table->foreign('processed_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('saving_transactions');
    }
};
