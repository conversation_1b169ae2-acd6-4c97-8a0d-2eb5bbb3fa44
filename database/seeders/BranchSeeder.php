<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $branches = [
            [
                'name' => 'Main Branch',
                'address' => 'Dhaka, Bangladesh - Main Office Building, Gulshan-1',
                'manager_id' => null, // Will be set after users are created
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Chittagong Branch',
                'address' => 'Chittagong, Bangladesh - Commercial Area, Agrabad',
                'manager_id' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Sylhet Branch',
                'address' => 'Sylhet, Bangladesh - City Center, Zindabazar',
                'manager_id' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Rajshahi Branch',
                'address' => 'Rajshahi, Bangladesh - New Market Area',
                'manager_id' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('branches')->insert($branches);
    }
}
