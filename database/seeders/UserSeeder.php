<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'member_id' => null,
                'branch_id' => 1, // Main Branch
                'is_active' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Main Branch Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('manager123'),
                'role' => 'manager',
                'member_id' => null,
                'branch_id' => 1,
                'is_active' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Chittagong Branch Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('manager123'),
                'role' => 'manager',
                'member_id' => null,
                'branch_id' => 2,
                'is_active' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Field Officer - Main',
                'email' => '<EMAIL>',
                'password' => Hash::make('officer123'),
                'role' => 'field_officer',
                'member_id' => null,
                'branch_id' => 1,
                'is_active' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Field Officer - Chittagong',
                'email' => '<EMAIL>',
                'password' => Hash::make('officer123'),
                'role' => 'field_officer',
                'member_id' => null,
                'branch_id' => 2,
                'is_active' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('users')->insert($users);

        // Update branch managers
        DB::table('branches')->where('id', 1)->update(['manager_id' => 2]);
        DB::table('branches')->where('id', 2)->update(['manager_id' => 3]);
    }
}
