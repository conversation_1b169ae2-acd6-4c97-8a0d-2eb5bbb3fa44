<?php

namespace Database\Seeders;

use App\Models\TransactionCategory;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TransactionCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUser = User::where('role', 'admin')->first();

        if (!$adminUser) {
            $this->command->error('No admin user found. Please create an admin user first.');
            return;
        }

        $categories = [
            // Income Categories
            ['name' => 'Loan Interest Income', 'type' => 'income', 'description' => 'Interest earned from loans'],
            ['name' => 'Service Charges', 'type' => 'income', 'description' => 'Various service charges'],
            ['name' => 'Late Payment Fees', 'type' => 'income', 'description' => 'Fees for late payments'],
            ['name' => 'Account Maintenance Fees', 'type' => 'income', 'description' => 'Monthly account maintenance charges'],
            ['name' => 'Processing Fees', 'type' => 'income', 'description' => 'Loan processing and documentation fees'],
            ['name' => 'Other Income', 'type' => 'income', 'description' => 'Miscellaneous income'],

            // Expense Categories
            ['name' => 'Staff Salaries', 'type' => 'expense', 'description' => 'Employee salaries and wages'],
            ['name' => 'Office Rent', 'type' => 'expense', 'description' => 'Monthly office rent'],
            ['name' => 'Utilities', 'type' => 'expense', 'description' => 'Electricity, water, internet bills'],
            ['name' => 'Transportation', 'type' => 'expense', 'description' => 'Vehicle fuel and maintenance'],
            ['name' => 'Office Supplies', 'type' => 'expense', 'description' => 'Stationery and office materials'],
            ['name' => 'Marketing & Advertising', 'type' => 'expense', 'description' => 'Promotional activities'],
            ['name' => 'Professional Services', 'type' => 'expense', 'description' => 'Legal, audit, consulting fees'],
            ['name' => 'Insurance', 'type' => 'expense', 'description' => 'Insurance premiums'],
            ['name' => 'Maintenance & Repairs', 'type' => 'expense', 'description' => 'Equipment and facility maintenance'],
            ['name' => 'Training & Development', 'type' => 'expense', 'description' => 'Staff training programs'],
            ['name' => 'Bad Debt Provision', 'type' => 'expense', 'description' => 'Provision for doubtful loans'],
            ['name' => 'Other Expenses', 'type' => 'expense', 'description' => 'Miscellaneous expenses'],
        ];

        foreach ($categories as $category) {
            TransactionCategory::create([
                'name' => $category['name'],
                'type' => $category['type'],
                'description' => $category['description'],
                'is_active' => true,
                'created_by' => $adminUser->id,
            ]);
        }

        $this->command->info('Transaction categories seeded successfully!');
    }
}
