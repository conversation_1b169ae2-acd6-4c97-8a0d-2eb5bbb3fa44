<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed in the correct order to handle foreign key dependencies
        $this->call([
            BranchSeeder::class,
            UserSeeder::class,
            MemberSeeder::class,
            AdvertisementSeeder::class,
        ]);
    }
}
