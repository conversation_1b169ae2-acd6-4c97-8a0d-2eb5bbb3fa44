<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $members = [
            [
                'member_id' => 'MEM001',
                'name' => '<PERSON><PERSON>',
                'father_or_husband_name' => '<PERSON>',
                'mother_name' => '<PERSON><PERSON>',
                'present_address' => 'House 15, Road 7, Dhanmondi, Dhaka-1205',
                'permanent_address' => 'Village: Sreepur, Upazila: Savar, District: Dhaka',
                'nid_number' => '1234567890123',
                'date_of_birth' => '1985-03-15',
                'religion' => 'Islam',
                'phone_number' => '***********',
                'blood_group' => 'B+',
                'photo' => null,
                'occupation' => 'Small Business',
                'reference_id' => null,
                'branch_id' => 1,
                'created_by' => 4, // Field Officer - Main
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'member_id' => 'MEM002',
                'name' => '<PERSON><PERSON>',
                'father_or_husband_name' => '<PERSON> Ali',
                'mother_name' => 'Salma Begum',
                'present_address' => 'Flat 3B, Building 12, Uttara, Dhaka-1230',
                'permanent_address' => 'Village: Mirzapur, Upazila: Tangail Sadar, District: Tangail',
                'nid_number' => '2345678901234',
                'date_of_birth' => '1990-07-22',
                'religion' => 'Islam',
                'phone_number' => '***********',
                'blood_group' => 'A+',
                'photo' => null,
                'occupation' => 'Tailoring',
                'reference_id' => 1, // Referenced by Fatima Begum
                'branch_id' => 1,
                'created_by' => 4,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'member_id' => 'MEM003',
                'name' => 'Nasir Ahmed',
                'father_or_husband_name' => 'Karim Uddin',
                'mother_name' => 'Rahima Begum',
                'present_address' => 'House 25, GEC Circle, Chittagong-4000',
                'permanent_address' => 'Village: Hathazari, Upazila: Hathazari, District: Chittagong',
                'nid_number' => '3456789012345',
                'date_of_birth' => '1982-11-10',
                'religion' => 'Islam',
                'phone_number' => '01934567890',
                'blood_group' => 'O+',
                'photo' => null,
                'occupation' => 'Rickshaw Puller',
                'reference_id' => null,
                'branch_id' => 2,
                'created_by' => 5, // Field Officer - Chittagong
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('members')->insert($members);
    }
}
